#!/bin/bash

# Riomavix网站部署脚本
# 使用方法: ./deploy.sh

set -e  # 遇到错误立即退出

echo "🚀 开始部署 Riomavix 网站..."

# 配置变量
PROJECT_DIR="/var/www/riomavix"
BACKUP_DIR="/var/backups/riomavix"
APP_NAME="riomavix-site"
BRANCH="main"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份当前版本
if [ -d "$PROJECT_DIR" ]; then
    echo "📦 备份当前版本..."
    BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
    cp -r $PROJECT_DIR $BACKUP_DIR/$BACKUP_NAME
    echo "✅ 备份完成: $BACKUP_DIR/$BACKUP_NAME"
fi

# 拉取最新代码
echo "📥 拉取最新代码..."
cd $PROJECT_DIR
git fetch origin
git reset --hard origin/$BRANCH

# 安装依赖
echo "📦 安装依赖..."
npm ci --production=false

# 构建项目
echo "🔨 构建项目..."
npm run build

# 重启应用
echo "🔄 重启应用..."
pm2 restart $APP_NAME

# 等待应用启动
echo "⏳ 等待应用启动..."
sleep 5

# 健康检查
echo "🏥 健康检查..."
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ 应用启动成功!"
else
    echo "❌ 应用启动失败，正在回滚..."
    
    # 回滚到最新备份
    LATEST_BACKUP=$(ls -t $BACKUP_DIR | head -n1)
    if [ -n "$LATEST_BACKUP" ]; then
        echo "🔄 回滚到: $LATEST_BACKUP"
        rm -rf $PROJECT_DIR
        cp -r $BACKUP_DIR/$LATEST_BACKUP $PROJECT_DIR
        cd $PROJECT_DIR
        pm2 restart $APP_NAME
        echo "✅ 回滚完成"
    else
        echo "❌ 没有找到备份文件"
    fi
    exit 1
fi

# 清理旧备份（保留最近5个）
echo "🧹 清理旧备份..."
cd $BACKUP_DIR
ls -t | tail -n +6 | xargs -r rm -rf

echo "🎉 部署完成!"
echo "📊 应用状态:"
pm2 status $APP_NAME
