# Riomavix网站部署指南

## 项目概述

这是一个基于Next.js 15的多语言企业网站，支持英文、中文、西班牙文三种语言，包含联系表单、SEO优化等功能。

## 部署选项

### 选项1：Vercel部署（推荐）

Vercel是Next.js的官方部署平台，提供最佳的性能和开发体验。

#### 步骤：

1. **准备代码仓库**
   ```bash
   # 如果还没有Git仓库，初始化一个
   git init
   git add .
   git commit -m "Initial commit"
   
   # 推送到GitHub/GitLab/Bitbucket
   git remote add origin <your-repository-url>
   git push -u origin main
   ```

2. **Vercel部署**
   - 访问 [vercel.com](https://vercel.com)
   - 使用GitHub账号登录
   - 点击"New Project"
   - 选择您的仓库
   - 配置环境变量（见下方环境变量配置）
   - 点击"Deploy"

3. **自定义域名**
   - 在Vercel项目设置中添加自定义域名
   - 配置DNS记录指向Vercel

### 选项2：传统服务器部署

#### 系统要求：
- Node.js 18.17或更高版本
- npm或yarn包管理器
- 至少1GB RAM
- 10GB可用磁盘空间

#### 部署步骤：

1. **服务器准备**
   ```bash
   # 更新系统
   sudo apt update && sudo apt upgrade -y
   
   # 安装Node.js 18+
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # 验证安装
   node --version
   npm --version
   ```

2. **安装PM2（进程管理器）**
   ```bash
   sudo npm install -g pm2
   ```

3. **上传项目文件**
   ```bash
   # 方法1：使用Git
   git clone <your-repository-url>
   cd site
   
   # 方法2：使用SCP上传
   scp -r /local/path/to/site user@server:/path/to/deployment
   ```

4. **安装依赖和构建**
   ```bash
   # 安装依赖
   npm install
   
   # 构建项目
   npm run build
   ```

5. **配置环境变量**
   ```bash
   # 创建生产环境变量文件
   cp .env.local .env.production
   # 编辑环境变量
   nano .env.production
   ```

6. **启动应用**
   ```bash
   # 使用PM2启动
   pm2 start npm --name "riomavix-site" -- start
   
   # 设置开机自启
   pm2 startup
   pm2 save
   ```

7. **配置Nginx反向代理**
   ```bash
   # 安装Nginx
   sudo apt install nginx
   
   # 创建配置文件
   sudo nano /etc/nginx/sites-available/riomavix
   ```

   Nginx配置内容：
   ```nginx
   server {
       listen 80;
       server_name your-domain.com www.your-domain.com;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

   ```bash
   # 启用站点
   sudo ln -s /etc/nginx/sites-available/riomavix /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

8. **配置SSL证书（Let's Encrypt）**
   ```bash
   # 安装Certbot
   sudo apt install certbot python3-certbot-nginx
   
   # 获取SSL证书
   sudo certbot --nginx -d your-domain.com -d www.your-domain.com
   ```

## 环境变量配置

### 必需的环境变量：

```bash
# 邮件配置
EMAIL_PASS=your-email-authorization-code
```

### Vercel环境变量设置：
1. 在Vercel项目设置中
2. 进入"Environment Variables"
3. 添加以下变量：
   - `EMAIL_PASS`: 您的邮箱授权码

### 服务器环境变量设置：
```bash
# 编辑 .env.production
EMAIL_PASS=your-actual-authorization-code
```

## 邮件服务配置

### 阿里云企业邮箱配置：
1. 登录阿里云企业邮箱管理后台
2. 获取SMTP配置信息
3. 生成应用专用密码
4. 将密码设置为`EMAIL_PASS`环境变量

### 163邮箱配置：
1. 登录163邮箱
2. 设置 → POP3/SMTP/IMAP
3. 开启SMTP服务
4. 生成授权码
5. 将授权码设置为`EMAIL_PASS`环境变量

## 域名和DNS配置

### DNS记录设置：
```
类型    名称    值
A       @       your-server-ip
A       www     your-server-ip
```

### Vercel域名配置：
```
类型    名称    值
CNAME   @       cname.vercel-dns.com
CNAME   www     cname.vercel-dns.com
```

## 监控和维护

### PM2常用命令：
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs riomavix-site

# 重启应用
pm2 restart riomavix-site

# 停止应用
pm2 stop riomavix-site

# 删除应用
pm2 delete riomavix-site
```

### 更新部署：
```bash
# 拉取最新代码
git pull origin main

# 安装新依赖（如有）
npm install

# 重新构建
npm run build

# 重启应用
pm2 restart riomavix-site
```

## 性能优化

### 1. 启用Gzip压缩
在Nginx配置中添加：
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

### 2. 设置缓存头
```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. 启用HTTP/2
```nginx
listen 443 ssl http2;
```

## 故障排除

### 常见问题：

1. **端口3000被占用**
   ```bash
   sudo lsof -i :3000
   sudo kill -9 <PID>
   ```

2. **权限问题**
   ```bash
   sudo chown -R $USER:$USER /path/to/site
   ```

3. **内存不足**
   ```bash
   # 增加swap空间
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

4. **邮件发送失败**
   - 检查环境变量是否正确设置
   - 验证邮箱SMTP设置
   - 检查服务器防火墙设置

## 安全建议

1. **定期更新系统和依赖**
2. **配置防火墙**
3. **使用强密码和SSH密钥**
4. **定期备份数据**
5. **监控服务器资源使用情况**

## 备份策略

### 自动备份脚本：
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /backup/riomavix_$DATE.tar.gz /path/to/site
find /backup -name "riomavix_*.tar.gz" -mtime +7 -delete
```

设置定时任务：
```bash
crontab -e
# 每天凌晨2点备份
0 2 * * * /path/to/backup.sh
```

## 推荐部署方案

**对于中小型企业网站，推荐使用Vercel部署**：
- ✅ 零配置部署
- ✅ 自动HTTPS
- ✅ 全球CDN
- ✅ 自动扩展
- ✅ 免费额度充足

**对于需要完全控制的企业，推荐VPS + Nginx + PM2方案**：
- ✅ 完全控制
- ✅ 自定义配置
- ✅ 成本可控
- ✅ 高性能
