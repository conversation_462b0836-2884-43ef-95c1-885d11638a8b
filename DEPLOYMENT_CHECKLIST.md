# 部署检查清单

## 部署前准备

### 代码准备
- [ ] 代码已提交到Git仓库
- [ ] 所有功能已测试完成
- [ ] 环境变量已配置
- [ ] 构建测试通过 (`npm run build`)
- [ ] 邮件功能已测试

### 服务器准备
- [ ] 服务器已准备就绪
- [ ] Node.js 18+ 已安装
- [ ] PM2 已安装
- [ ] Nginx 已安装并配置
- [ ] 域名DNS已配置
- [ ] SSL证书已配置

## 快速部署命令

### 1. Vercel部署（推荐）
```bash
# 安装Vercel CLI
npm i -g vercel

# 登录Vercel
vercel login

# 部署
vercel --prod
```

### 2. 服务器部署
```bash
# 1. 上传代码到服务器
git clone <your-repo-url>
cd site

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.local .env.production
nano .env.production

# 4. 构建项目
npm run build

# 5. 启动应用
pm2 start ecosystem.config.js --env production

# 6. 配置Nginx
sudo cp nginx.conf /etc/nginx/sites-available/riomavix
sudo ln -s /etc/nginx/sites-available/riomavix /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx

# 7. 配置SSL
sudo certbot --nginx -d your-domain.com
```

## 环境变量配置

### 生产环境变量
```bash
# .env.production
EMAIL_PASS=your-actual-email-password
NODE_ENV=production
```

### Vercel环境变量
在Vercel控制台设置：
- `EMAIL_PASS`: 邮箱授权码

## 部署后验证

### 功能测试
- [ ] 网站可以正常访问
- [ ] 多语言切换正常
- [ ] 联系表单可以发送邮件
- [ ] 所有页面链接正常
- [ ] 移动端显示正常
- [ ] SEO标签正确

### 性能测试
- [ ] 页面加载速度 < 3秒
- [ ] 图片正常加载
- [ ] CSS/JS文件正常加载
- [ ] HTTPS证书有效

### 监控设置
- [ ] PM2监控正常
- [ ] Nginx日志正常
- [ ] 错误日志监控
- [ ] 服务器资源监控

## 常用维护命令

```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs riomavix-site

# 重启应用
pm2 restart riomavix-site

# 更新部署
./deploy.sh

# 查看Nginx状态
sudo systemctl status nginx

# 重新加载Nginx配置
sudo nginx -s reload
```

## 故障排除

### 应用无法启动
1. 检查端口是否被占用: `sudo lsof -i :3000`
2. 检查环境变量: `pm2 env riomavix-site`
3. 查看错误日志: `pm2 logs riomavix-site --err`

### 邮件发送失败
1. 检查环境变量是否正确
2. 测试SMTP连接
3. 检查防火墙设置

### 域名无法访问
1. 检查DNS解析: `nslookup your-domain.com`
2. 检查Nginx配置: `sudo nginx -t`
3. 检查SSL证书: `sudo certbot certificates`

## 备份和恢复

### 创建备份
```bash
# 手动备份
tar -czf riomavix_backup_$(date +%Y%m%d).tar.gz /var/www/riomavix

# 自动备份（添加到crontab）
0 2 * * * /path/to/backup.sh
```

### 恢复备份
```bash
# 停止应用
pm2 stop riomavix-site

# 恢复文件
tar -xzf riomavix_backup_YYYYMMDD.tar.gz -C /

# 重启应用
pm2 start riomavix-site
```

## 安全建议

- [ ] 定期更新系统和依赖
- [ ] 使用强密码和SSH密钥
- [ ] 配置防火墙
- [ ] 定期备份数据
- [ ] 监控异常访问
- [ ] 保持SSL证书更新

## 联系信息

如果在部署过程中遇到问题，请检查：
1. 服务器日志
2. 应用日志
3. Nginx日志
4. 系统资源使用情况
