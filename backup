server {
    listen 80;
    server_name www.medixbuy.com;  # 使用你的公网 IP 地址，或者你可以使用你自己的域名

    root /root/riomavix/riomavix/public;  # Laravel 项目的 public 目录
    index index.php index.html index.htm;

    # 配置日志文件
    access_log /var/log/nginx/riomavix_access.log;
    error_log /var/log/nginx/riomavix_error.log;

    # Laravel 请求处理
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 处理 PHP 文件
    location ~ \.php$ {
        fastcgi_pass unix:/run/php-fpm/www.sock;  # PHP-FPM 的地址
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 禁止访问 .env 和其他敏感文件
    location ~ /\. {
        deny all;
    }

    # 设置缓存头
    location ~* \.(jpg|jpeg|png|gif|css|js|ico)$ {
        expires 1y;
        access_log off;
    }
}