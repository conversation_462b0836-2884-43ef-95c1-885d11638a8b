# Contact Form Testing Guide

## 功能概述

Contact Us页面现在包含完整的联系表单功能，支持三种语言（英文、中文、西班牙文）。

## 实现的功能

### 1. 表单验证
- **姓名 (Your Name)**: 必填，最多50个字符
- **邮箱 (Your Email)**: 必填，最多50个字符，必须是有效邮箱格式
- **公司 (Your Company)**: 必填，最多50个字符
- **需求说明**: 必填，最多500个字符，带字符计数器

### 2. 表单提交
- 点击Send按钮后，表单内容会生成邮件发送到 `<EMAIL>`
- 发送成功后显示成功提示消息
- 发送成功后，Send按钮1分钟内不可用
- 提交过程中显示加载状态

### 3. 多语言支持
- **英文**: 所有验证消息和界面文本为英文
- **中文**: 所有验证消息和界面文本为中文
- **西班牙文**: 所有验证消息和界面文本为西班牙文

## 测试步骤

### 1. 访问Contact页面
- 英文版: http://localhost:3001/en/contact/
- 中文版: http://localhost:3001/zh/contact/
- 西班牙文版: http://localhost:3001/es/contact/

### 2. 测试表单验证
1. 尝试提交空表单 - 应该显示所有必填字段的错误消息
2. 输入超过50个字符的姓名/邮箱/公司 - 应该显示字符限制错误
3. 输入无效邮箱格式 - 应该显示邮箱格式错误
4. 输入超过500个字符的需求说明 - 应该显示字符限制错误

### 3. 测试成功提交
1. 填写所有必填字段（符合字符限制）
2. 点击Send按钮
3. 观察加载状态和成功提示
4. 验证按钮在1分钟内不可用

## 邮件配置

### 当前配置
- 使用163.com SMTP服务
- 发送到: <EMAIL>
- 需要在 `.env.local` 中配置 `EMAIL_PASS`

### 配置步骤
1. 登录163邮箱
2. 进入设置 > POP3/SMTP/IMAP
3. 开启SMTP服务
4. 生成授权码
5. 在 `.env.local` 中设置 `EMAIL_PASS=你的授权码`

## 邮件内容格式

邮件包含以下信息：
- 发件人信息（姓名、邮箱、公司）
- 需求说明
- 提交时间
- 语言版本
- Riomavix品牌标识

## 技术特性

### 前端验证
- 实时字符计数
- 即时错误提示
- 表单状态管理
- 防重复提交

### 后端处理
- 服务器端验证
- 邮件模板生成
- 错误处理
- 多语言邮件内容

### 用户体验
- 加载状态指示
- 成功/失败反馈
- 按钮禁用机制
- 响应式设计

## 故障排除

### 常见问题
1. **邮件发送失败**: 检查 `.env.local` 中的 `EMAIL_PASS` 配置
2. **表单验证不工作**: 确保JavaScript已启用
3. **按钮不响应**: 检查浏览器控制台错误信息

### 调试信息
- 查看浏览器开发者工具的Network标签
- 检查服务器控制台的错误日志
- 验证API路由 `/api/send-email` 是否正常工作
