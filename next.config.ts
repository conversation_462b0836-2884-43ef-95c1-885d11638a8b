import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n.ts');

const nextConfig: NextConfig = {
  // 动态模式配置
  // 移除 trailingSlash，动态模式下不需要
  // 启用图片优化
  images: {
    domains: ['riomavix.com'], // 添加允许的图片域名
    formats: ['image/webp', 'image/avif'], // 启用现代图片格式
  },
  // 启用实验性功能
  experimental: {
    optimizePackageImports: ['@/components', '@/lib'],
  }
};

export default withNextIntl(nextConfig);
