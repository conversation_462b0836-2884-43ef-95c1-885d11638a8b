export default async function MedixbuyPage({
  params
}: {
  params: Promise<{locale: string}>;
}) {
  const {locale} = await params;
  
  // Get page content based on locale
  const getContent = (key: string) => {
    const content: Record<string, Record<string, string>> = {
      en: {
        title: 'MEDIXBUY',
        subtitle: 'Your trusted medical procurement platform'
      },
      zh: {
        title: 'MEDIXBUY',
        subtitle: '您值得信赖的医疗采购平台'
      },
      es: {
        title: 'MEDIXBUY',
        subtitle: 'Su plataforma confiable de adquisiciones médicas'
      }
    };
    return content[locale]?.[key] || content.en[key];
  };

  return (
    <div className="container mx-auto px-4 py-16">
      {/* Page Header */}
      <section className="text-center mb-16">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          {getContent('title')}
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          {getContent('subtitle')}
        </p>
      </section>

      {/* Content sections can be added here */}
      <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-4">Medical Equipment</h3>
          <p className="text-gray-600">High-quality medical equipment for healthcare facilities.</p>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-4">Pharmaceuticals</h3>
          <p className="text-gray-600">Comprehensive pharmaceutical solutions and supplies.</p>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-4">Digital Platform</h3>
          <p className="text-gray-600">Advanced digital procurement platform for efficiency.</p>
        </div>
      </section>
    </div>
  );
}
