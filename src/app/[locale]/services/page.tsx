 'use client';

'use client';

import { useState, use, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import SEO from '@/components/SEO';
import { getSEOConfig } from '@/lib/seo-config';

export default  function ServicesPage({
  params
}: {
  params: Promise<{locale: string}>;
}) {
  const {locale} = use(params);
  const seoConfig = getSEOConfig(locale, 'services');
  const searchParams = useSearchParams();
  const [activeService, setActiveService] = useState('authorised');

  // Handle URL parameter for service selection
  useEffect(() => {
    const serviceParam = searchParams.get('service');
    if (serviceParam && ['authorised', 'distribution', 'importer', 'reporting', 'medixbuy'].includes(serviceParam)) {
      setActiveService(serviceParam);
    }
  }, [searchParams]);

  // Get page content based on locale
  const getContent = (key: string) => {
    const content: Record<string, any> = {
      en: {
        title: 'Our Services',
        subtitle: 'What we offer',
        servicesMenu: 'Services Menu',
        contentComingSoon: 'Content Coming Soon',
        detailedInfo: 'Detailed information about',
        willBeAdded: 'will be added here.',
        contactUs: 'Looking for a trusted Authorized Representative in the EU? [Contact us]',
        contactUsForMoreInfo: 'Contact Us For More Information',

        // Service menu items
        authorised: {
          main: 'Authorised',
          sub: 'EU Authorised Representative',
          authorisedList:[
            "As the legal representative of non-EU manufacturers, Riomavix acts as the official liaison with EU authorities.",
            "We ensure compliance with MDR, IVDR, and GPSR requirements, manage unannounced inspections, and handle incident reporting and product recalls.",
            "We also assist with product registration and apply for Free Sales Certificates (FSC) to facilitate exports to countries that recognize EU standards."
          ]
        },
        distribution: {
          main: 'Distribution',
          sub: 'European Distribution Services',
          serviceFeatures: 'SERVICE FEATURES',
          cooperationModel: 'COOPERATION MODEL',
          riomavixProvides: 'Riomavix Provides',
          serviceFeaturesList: [
            'We provide a detailed process and budget for market expansion of the selected products or equipment for our clients.',
            'The objective is to reach distribution agreements, and based on exclusive regulations, to obtain as many partners and distributors as possible (exclusive or non-exclusive), added by the supplier.',
            'Riomavix provides training to our supplier clients to support them in selling their products or equipment in different countries, and our role is to convey information to our partners and distributors, creating strategic partnerships for our clients.',
            'We are also committed to establishing a dedicated database to thoroughly research and connect with new clients.',
            'During the trial period, we will initially operate in Spain, France, Italy, and Germany, with the scope ultimately determined by the client\'s selected countries.',
            'Our goal is to reach distribution agreements and, depending on applicable regulations, secure as many partners and distributors as possible — whether exclusive or non-exclusive — as selected by the supplier.',
            'Riomavix will identify and verify professional sales teams, stable client bases, and potential distributors with experience in the selected product categories or equipment.',
            'Riomavix will always follow the supplier\'s requirements, communicate, and achieve a close cooperation agreement between the end client and our client.'
          ],
          cooperationModelList: [
            'This agreement is based on success. Riomavix will organize meetings between the identified distributors and suppliers, allowing suppliers to directly showcase the main features and benefits of their products.',
            'Riomavix will act as an intermediary throughout the process, facilitating communication and understanding between both parties.',
            'Riomavix will always act as a mediator between both parties.'
          ],
          riomavixProvidesList: [
            'Market entry strategy and budgeting',
            'Distributor identification and qualification',
            'Sales team coordination and product training',
            'Direct meetings between suppliers and partners'
          ]
        },
        importer: {
          main: 'Importer',
          sub: 'Importer Services',
          importerList: [
            'Importers are individuals or companies established in the Union placing third-country products on the EU market, following obligations in Article 13 of MDR and IVDR.',
            'Riomavix offers competitive and transparent fees for storage and transportation',
            'Specialized import services for non-EU manufacturers to deliver sanitary products closer to end customers.',
            'Collaboration with trusted customs agents to ensure efficient and compliant import processes.',
            'Licensed pharmaceutical storage solutions available in Spain for secure handling of products.'
          ]
        },
        reporting: {
          main: 'Reporting',
          sub: 'Customized Reporting Service',
          reportingIntro: 'We offer two types of customized reports to support the expansion and development of your business:',
          supplierSearchReport: 'Supplier Search Report',
          supplierSearchDesc: 'Ideal for those looking for specific products or devices. We provide a list of potential suppliers with the most relevant data.',
          customerSearchReport: 'End Customer or Distributor Search Report',
          customerSearchDesc: [
            'Designed for companies wishing to introduce their products in new markets through a network of strategic contacts in specific countries.',
            'Each report is prepared after an initial market study. We send a free sample with two options and basic data to refine the client\'s criteria before final delivery.',
            'Our reports are especially useful for accessing hard-to-reach markets, providing verified and strategic information to clearly guide your project.'
          ]
        },
        medixbuy: {
          main: 'MedixBuy',
          sub: 'Online Platform Services',
          medixbuyIntro: 'MedixBuy is a healthcare showcase platform not a traditional marketplace:',
          sellersCan: 'Sellers Can:',
          buyersCan: 'Buyers Can:',
          weFacilitate: 'We Facilitate:',
          sellersCanList: [
            'Publish their product catalogs',
            'Connect with potential customers'
          ],
          buyersCanList: [
            'Find specific products',
            'Request information'
          ],
          weFacilitateList: [
            'Contact between buyers and sellers'
          ],
          transactionNote: 'Note: All transactions are managed outside the platform'
        }
      },
      zh: {
        title: '我们的服务',
        subtitle: '我们提供的服务',
        servicesMenu: '服务菜单',
        contentComingSoon: '内容即将推出',
        detailedInfo: '关于',
        willBeAdded: '的详细信息将在此处添加。',
        contactUs: '联系我们',
        contactUsForMoreInfo: '联系我们获取更多信息',

        // Service menu items
        authorised: {
          main: '授权代表',
          sub: '欧盟授权代表',
          authorisedList:[
            "作为非欧盟制造商的法定代表，Riomavix担任与欧盟官方机构的联络人。",
            "确保MDR/IVDR/GPSR合规,管理突击检查与事件通报及产品召回",
            "协助产品注册及申请自由销售证书（FSC），便利出口至认可欧盟标准的国家"
          ]
        },
        distribution: {
          main: '分销服务',
          sub: '欧洲分销服务',
          serviceFeatures: '服务特点',
          cooperationModel: '合作模式',
          riomavixProvides: 'Riomavix 提供',
          serviceFeaturesList: [
            '为客户选定产品或设备提供详细的市场拓展流程与预算方案。',
            '目标是通过独家或非独家协议，最大化合作伙伴与分销商数量。',
            'Riomavix为供应商提供培训，支持其在多国销售产品，并负责向合作伙伴传递信息，建立战略联盟。',
            '承诺建立专用数据库，深度调研并对接新客户。',
            '初期试点阶段覆盖西班牙、法国、意大利与德国，最终范围由客户选定国家决定。',
            '我们的目标是达成分销协议，并根据适用的法规，尽可能多地确保合作伙伴和分销商——无论是独家还是非独家——由供应商选定。',
            '识别并验证具备稳定客户基础及目标产品经验的专业销售团队与潜在分销商。',
            '遵循供应商要求，促进终端客户与供应商达成紧密合作协议。'
          ],
          cooperationModelList: [
            '基于成果的合作协议：Riomavix组织供应商与分销商会议，由供应商直接介绍产品特性与优势。',
            '全程担任沟通中介，促进双方理解。',
            '持续作为双方协调人。'
          ],
          riomavixProvidesList: [
            '市场准入策略与预算',
            '分销商识别与资质审核',
            '销售团队协调与产品培训',
            '供应商与合作伙伴直接会议'
          ]
        },
        importer: {
          main: '进口服务',
          sub: '进口服务',
          importerList: [
            '进口商为在欧盟境内设立的个人或企业，负责将第三国产品引入欧盟市场，需履行MDR/IVDR第13条义务。',
            'Riomavix提供具有竞争力的仓储与运输费用',
            '为非欧盟制造商提供进口服务，将医疗产品配送至更接近终端客户的地点',
            '与可信报关代理合作，确保高效合规的进口流程',
            '提供西班牙许可药品仓储解决方案，安全处理产品'
          ]
        },
        reporting: {
          main: '定制报告服务',
          sub: '定制报告服务',
          reportingIntro: '我们提供两类定制报告，支持业务拓展：',
          supplierSearchReport: '供应商搜索报告',
          supplierSearchDesc: '适用于寻找特定产品或设备的客户，提供包含核心数据的潜在供应商清单',
          customerSearchReport: '终端客户或分销商搜索报告',
          customerSearchDesc: [
            '针对希望进入新市场的企业，通过目标国家的战略联系人网络推广产品',
            '每份报告基于初步市场调研编制。我们提供含两项选项及基础数据的免费样本，根据客户反馈完善后交付最终版本。',
            '报告尤其适用于难以直接触达的市场，提供经过验证的战略信息以明确项目方向。'
          ]
        },
        medixbuy: {
          main: 'MedixBuy在线平台服务',
          sub: '在线平台服务',
          medixbuyIntro: 'MedixBuy是医疗健康领域的展示平台（非传统市场）',
          sellersCan: '卖家可：',
          buyersCan: '买家可：',
          weFacilitate: '我们促进：',
          sellersCanList: [
            '发布产品目录',
            '对接潜在客户'
          ],
          buyersCanList: [
            '查找特定产品',
            '申请信息'
          ],
          weFacilitateList: [
            '买卖双方对接服务'
          ],
          transactionNote: '注意：所有交易都在平台外完成'
        }
      },
      es: {
        title: 'Nuestros Servicios',
        subtitle: 'Lo que ofrecemos',
        servicesMenu: 'Menú de Servicios',
        contentComingSoon: 'Contenido Próximamente',
        detailedInfo: 'Información detallada sobre',
        willBeAdded: 'se agregará aquí.',
        contactUs: '¿Necesitas un Representante Autorizado en la UE? [Contáctanos]',
        contactUsForMoreInfo: 'Contáctanos para más información',

        // Service menu items
        authorised: {
          main: 'Autorizado',
          sub: 'Representante Autorizado UE',
          authorisedList:[
            "Como representante legal de fabricantes fuera de la UE, Riomavix actúa como enlace oficial con las autoridades de la UE.",
            "Garantizamos el cumplimiento de los requisitos de MDR, IVDR y GPSR, gestionamos inspecciones no anunciadas y manejamos la notificación de incidentes y retiradas de productos.",
            "También asistimos en el registro de productos y solicitamos Certificados de Venta Libre (FSC) para facilitar las exportaciones a países que reconocen los estándares de la UE."
          ]
        },
        distribution: {
          main: 'Distribución',
          sub: 'Servicios de Distribución Europea',
          serviceFeatures: 'CARACTERÍSTICAS DEL SERVICIO',
          cooperationModel: 'MODELO DE COOPERACIÓN',
          riomavixProvides: 'Riomavix Proporciona',
          serviceFeaturesList: [
            'Proporcionamos un proceso detallado y presupuesto por la expansión de mercado de los productos o equipos seleccionados para nuestros clientes.',
            'El objetivo es alcanzar acuerdos de distribución y, según regulaciones exclusivas, obtener el mayor número posible de socios y distribuidores (exclusivos o no exclusivos), designados por el proveedor.',
            'Riomavix ofrece capacitación a nuestros clientes proveedores para apoyarles en la venta de sus productos o equipos en diferentes países, y nuestro rol es transmitir información a nuestros socios y distribuidores, creando alianzas estratégicas para nuestros clientes.',
            'También nos comprometemos a establecer una base de datos dedicada a investigar a fondo y conectar con nuevos clientes.',
            'Durante el período de prueba, operaremos inicialmente en España, Francia, Italia y Alemania, con el alcance determinado finalmente por los países seleccionados por el cliente.',
            'Nuestro objetivo es conectar con clientes finales a través de distribuidores o relacionarnos directamente con ellos. Apoyamos ambas modalidades.',
            'Riomavix identificará y verificará equipos de ventas profesionales, bases de clientes estables y distribuidores potenciales con experiencia en las categorías de productos o equipos seleccionados.',
            'Riomavix siempre seguirá los requisitos del proveedor, mantendrá la comunicación y logrará un acuerdo de cooperación estrecha entre el cliente final y nuestro cliente.'
          ],
          cooperationModelList: [
            'Este acuerdo se basa en el éxito. Riomavix organizará reuniones entre los distribuidores identificados y los proveedores, permitiendo que los proveedores presenten directamente las principales características y beneficios de sus productos.',
            'Riomavix actuará como intermediario durante todo el proceso, facilitando la comunicación y el entendimiento entre ambas partes.',
            'Riomavix siempre actuará como mediador entre ambas partes.'
          ],
          riomavixProvidesList: [
            'Estrategia de entrada al mercado y presupuesto',
            'Identificación y calificación de distribuidores',
            'Coordinación del equipo de ventas y capacitación de productos',
            'Reuniones directas entre proveedores y socios'
          ]
        },
        importer: {
          main: 'Importación',
          sub: 'Servicios de Importación',
          importerList: [
            'Los importadores son personas físicas o empresas establecidas en la Unión que introducen productos de terceros países en el mercado de la UE, cumpliendo con las obligaciones del Artículo 13 del MDR e IVDR.',
            'Riomavix ofrece tarifas competitivas y transparentes para almacenamiento y transporte.',
            'Servicios especializados de importación para fabricantes fuera de la UE, para entregar productos sanitarios más cerca de los clientes finales.',
            'Colaboración con agentes de aduanas de confianza para asegurar procesos de importación eficientes y conformes.',
            'Soluciones de almacenamiento farmacéutico con licencia disponibles en España para el manejo seguro de productos.'
          ]
        },
        reporting: {
          main: 'Informes',
          sub: 'Servicio de Informes Personalizados',
          reportingIntro: 'Ofrecemos dos tipos de informes personalizados para apoyar la expansión y desarrollo de tu negocio:',
          supplierSearchReport: 'Informe de Búsqueda de Proveedores',
          supplierSearchDesc: 'Ideal para quienes buscan productos o dispositivos específicos. Proporcionamos un listado de posibles proveedores con los datos más relevantes.',
          customerSearchReport: 'Informe de Búsqueda de Cliente Final o Distribuidor',
          customerSearchDesc: [
            'Pensado para empresas que desean introducir sus productos en nuevos mercados mediante una red de contactos estratégicos en países específicos.',
            'Cada informe se elabora tras un estudio de mercado inicial. Enviamos una muestra gratuita con dos opciones y datos básicos para afinar los criterios del cliente antes de la entrega final.',
            'Nuestros informes son especialmente útiles para acceder a mercados difíciles de alcanzar, brindando información verificada y estratégica para orientar con claridad tu proyecto.'
          ]
        },
        medixbuy: {
          main: 'MedixBuy',
          sub: 'Servicios de Plataforma Online',
          medixbuyIntro: 'MedixBuy es una plataforma de exhibición para el sector salud, no un mercado tradicional:',
          sellersCan: 'Los Vendedores Pueden:',
          buyersCan: 'Los Compradores Pueden:',
          weFacilitate: 'Facilitamos:',
          sellersCanList: [
            'Publicar sus catálogos de productos',
            'Conectar con clientes potenciales'
          ],
          buyersCanList: [
            'Encontrar productos específicos',
            'Solicitar información'
          ],
          weFacilitateList: [
            'Contacto entre compradores y vendedores'
          ],
          transactionNote: 'Nota: Todas las transacciones se gestionan fuera de la plataforma'
        }
      }
    };
    return content[locale]?.[key] || content.en[key];
  };

  const services = [
    {
      id: 'authorised',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      ),
      main: getContent('authorised').main,
      sub: getContent('authorised').sub,
      detail:getContent('authorised').detail
    },
    {
      id: 'distribution',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4l3 3m6 0l3-3m-3 3v12m0-12l-3 3m6 0l3-3m-3 3v12m0-12l-3 3" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8l5 5 5-5" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l-5-5-5 5" />
        </svg>
      ),
      main: getContent('distribution').main,
      sub: getContent('distribution').sub,
      // content:getContent('distribution')
    },
    {
      id: 'importer',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 18h18l-2-6H5l-2 6zM21 18v1a2 2 0 01-2 2H5a2 2 0 01-2-2v-1" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8V4m0 0l-3 3m3-3l3 3" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h8" />
        </svg>
      ),
      main: getContent('importer').main,
      sub: getContent('importer').sub
    },
    {
      id: 'reporting',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      main: getContent('reporting').main,
      sub: getContent('reporting').sub
    },
    {
      id: 'medixbuy',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
        </svg>
      ),
      main: getContent('medixbuy').main,
      sub: getContent('medixbuy').sub
    }
  ];

  return (
    <div className={`min-h-screen ${locale === 'zh' ? 'text-lg' : ''}`}>
      <SEO
        title={seoConfig.title}
        description={seoConfig.description}
        keywords={seoConfig.keywords}
        locale={locale}
        path={`/${locale}/services`}
      />
      {/* Header Section */}
       <section className="bg-primary text-white py-10">
        <div className="container mx-auto px-6 lg:px-12">
          <h1 className={`${locale === 'zh' ? 'text-5xl md:text-6xl' : 'text-4xl md:text-5xl'} font-black font-roboto text-center`}>
            {getContent('title')}
          </h1>
        </div>
      </section>

      {/* Services Content */}
      <section className="bg-gray-50 min-h-screen">
        <div className="container mx-auto px-6 lg:px-12 py-16">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Sidebar */}
            <div className="lg:w-1/3">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                {/* <div className="p-6 bg-blue-600 text-white">
                  <h2 className="text-xl font-bold font-roboto">{getContent('servicesMenu')}</h2>
                </div> */}
                <nav className="p-0">
                  {services.map((service, index) => (
                    <button
                      key={service.id}
                      onClick={() => setActiveService(service.id)}
                      className={`w-full text-left p-6 border-b border-gray-200 transition-all duration-200 hover:bg-blue-50 ${
                        activeService === service.id
                          ? 'bg-blue-50 border-l-4 border-l-primary'
                          : 'hover:border-l-4 hover:border-l-blue-300'
                      } ${index === services.length - 1 ? 'border-b-0' : ''}`}
                    >
                      <div className="flex items-start space-x-4">
                        <div className={`flex-shrink-0 p-2 rounded-lg ${
                          activeService === service.id
                            ? 'bg-primary text-white'
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {service.icon}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className={`${locale === 'zh' ? 'text-xl' : 'text-lg'} font-bold font-roboto mb-1 ${
                            activeService === service.id ? 'text-primary' : 'text-gray-900'
                          }`}>
                            {service.main}
                          </h3>
                          <p className={`${locale === 'zh' ? 'text-base' : 'text-sm'} font-bold ${
                            activeService === service.id ? 'text-primary' : 'text-gray-400'
                          }`}>
                            {service.sub}
                          </p>
                        </div>
                      </div>
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Content Area */}
            <div className="lg:w-2/3">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 min-h-[600px]">
                <div className="p-8">
                  <div className="flex items-center space-x-4 mb-6">
                    {/* <div className="p-3 bg-blue-100 rounded-lg text-blue-600">
                      {services.find(s => s.id === activeService)?.icon}
                    </div> */}
                    <div className="border-b-2 border-gray-500">
                      <h2 className="text-2xl font-bold font-roboto text-gray-900 mb-1 display-block">
                         {services.find(s => s.id === activeService)?.sub}
                      </h2>
                    </div>
                    
                  </div>
                  {/* EU Authorised Representative */}
                  <div className={`${
                            activeService === 'authorised' ? '' : 'hidden'
                          }`}>
                       <div className="px-10 py-6 bg-gray-100 rounded-lg ">
                    <ul className="space-y-1 list-disc text-gray-900">
                          {getContent('authorised').authorisedList.map((feature: string, index: number) => (
                      <li key={index}>{feature}</li>
                    ))}
                          </ul>
                  </div>
                  <div className="px-10 py-6  rounded-lg space-y-2 mb-5 text-center">
                       <a
                            href={`/${locale}/contact/`}
                            className="inline-flex items-center justify-center bg-white border-2 border-cyan-500 text-cyan-500 px-8 py-3 rounded-full font-semibold text-sm hover:bg-cyan-500 hover:text-white transition-all duration-300 shadow-sm hover:shadow-md"
                          >
                       {getContent('contactUs')}  
                </a>
                    </div>
                  </div>

                 {/* European Distribution Services */}
                  <div className={`${
                            activeService === 'distribution' ? '' : 'hidden'
                          }`}>
                  <div className="px-10 py-6 bg-gray-100 rounded-lg space-y-2 mb-5">
                    <h2 className="text-xl font-black font-roboto text-primary mb-1">{getContent('distribution').serviceFeatures}</h2>
                    {getContent('distribution').serviceFeaturesList.map((feature: string, index: number) => (
                      <p className="text-lg" key={index}>{feature}</p>
                    ))}
                  </div>
                  <div className="px-10 py-6 bg-gray-100 rounded-lg space-y-2 mb-5 ">
                    <h2 className="text-xl font-black font-roboto text-primary mb-1">{getContent('distribution').cooperationModel}</h2>
                    {getContent('distribution').cooperationModelList.map((feature: string, index: number) => (
                      <p className="text-lg" key={index}>{feature}</p>
                    ))}
                             </div>

                   <div className="px-10 py-6 bg-gray-100 rounded-lg space-y-2 mb-5">
                    <h2 className="text-xl font-black font-roboto text-primary mb-1">{getContent('distribution').riomavixProvides}</h2>
                       <ul className="space-y-1 list-disc ml-4">
                          {getContent('distribution').riomavixProvidesList.map((feature: string, index: number) => (
                      <li className="text-lg" key={index}>{feature}</li>
                    ))}
                    </ul>
                   </div>

                    <div className="px-10 py-6  rounded-lg space-y-2 mb-5 text-center">
                       <a
                            href={`/${locale}/contact/`}
                            className="inline-flex items-center justify-center bg-white border-2 border-cyan-500 text-cyan-500 px-8 py-3 rounded-full font-semibold text-sm hover:bg-cyan-500 hover:text-white transition-all duration-300 shadow-sm hover:shadow-md"
                          >
                       {getContent('contactUsForMoreInfo')}   
                </a>
                    </div>
                  </div>

                   {/* Importer */}
                  <div className={`${
                            activeService === 'importer' ? '' : 'hidden'
                          }`}>
                    <div className="px-10 py-6 bg-gray-100 rounded-lg space-y-2 mb-5">
                         <ul className="space-y-1 list-disc ml-4 ">
                                  {getContent('importer').importerList.map((feature: string, index: number) => (
                            <li key={index}>{feature}</li>
                          ))}
                    </ul>
                    </div>
                     <div className="px-10 py-6  rounded-lg space-y-2 mb-5 text-center">
                       <a
                            href={`/${locale}/contact/`}
                            className="inline-flex items-center justify-center bg-white border-2 border-cyan-500 text-cyan-500 px-8 py-3 rounded-full font-semibold text-sm hover:bg-cyan-500 hover:text-white transition-all duration-300 shadow-sm hover:shadow-md"
                          >
                       {getContent('contactUs')}  
                </a>
                    </div>
                  </div>

                   {/* Reporting */}
                  <div className={`${
                            activeService === 'reporting' ? '' : 'hidden'
                          }`}>
                    <div className="px-4 py-1 rounded-lg space-y-2 mb-5">
                        <p>{getContent('reporting').reportingIntro}</p>
                    </div>
                    <div className="px-10 py-6 bg-gray-100 rounded-lg space-y-2 mb-5">
                        <h2 className="text-xl font-black font-roboto text-primary mb-1">{getContent('reporting').supplierSearchReport}</h2>
                        <p>{getContent('reporting').supplierSearchDesc}</p>
                    </div>
                    <div className="px-10 py-6 bg-gray-100 rounded-lg space-y-2 mb-5">
                        <h2 className="text-xl font-black font-roboto text-primary mb-1">{getContent('reporting').customerSearchReport}</h2>
                        {getContent('reporting').customerSearchDesc.map((feature: string, index: number) => (
                      <p key={index}>{feature}</p>
                    ))}
                    </div>
                     <div className="px-10 py-6  rounded-lg space-y-2 mb-5 text-center">
                       <a
                            href={`/${locale}/contact/`}
                            className="inline-flex items-center justify-center bg-white border-2 border-cyan-500 text-cyan-500 px-8 py-3 rounded-full font-semibold text-sm hover:bg-cyan-500 hover:text-white transition-all duration-300 shadow-sm hover:shadow-md"
                          >
                       {getContent('contactUs')}  
                </a>
                    </div>
                  </div>

                  {/* MedixBuy */}
                  <div className={`${
                            activeService === 'medixbuy' ? '' : 'hidden'
                          }`}>
                    <div className="px-4 py-1  rounded-lg space-y-2 mb-5">
                        <p>{getContent('medixbuy').medixbuyIntro}</p>
                    </div>
                    <div className="px-10 py-6 bg-gray-100 rounded-lg space-y-2 mb-5">
                        <h2 className="text-xl font-black font-roboto text-primary mb-3">{getContent('medixbuy').sellersCan}</h2>
                           <ul className="space-y-2 list-disc ml-4">
                          {getContent('medixbuy').sellersCanList.map((feature: string, index: number) => (
                      <li key={index}>{feature}</li>
                    ))}
                    </ul>
                   </div>
                    <div className="px-10 py-6 bg-gray-100 rounded-lg space-y-2 mb-5">
                        <h2 className="text-xl font-black font-roboto text-primary mb-3">{getContent('medixbuy').buyersCan}</h2>
                        <ul className="space-y-2 list-disc ml-4">
                          {getContent('medixbuy').buyersCanList.map((feature: string, index: number) => (
                      <li key={index}>{feature}</li>
                    ))}
                    </ul>
                    </div>
                     <div className="px-10 py-6 bg-gray-100 rounded-lg space-y-2 mb-5">
                        <h2 className="text-xl font-black font-roboto text-primary mb-3">{getContent('medixbuy').weFacilitate}</h2>
                        <ul className="space-y-2 list-disc ml-4">
                          {getContent('medixbuy').weFacilitateList.map((feature: string, index: number) => (
                      <li key={index}>{feature}</li>
                    ))}
                    </ul>
                    </div>
                     <div className="px-4 py-1 rounded-lg space-y-2 mb-5">
                        <p>{getContent('medixbuy').transactionNote}</p>
                    </div>
                     <div className="px-10 py-6  rounded-lg space-y-2 mb-5 text-center">
                       <a
                            href={`/${locale}/contact/`}
                            className="inline-flex items-center justify-center bg-white border-2 border-cyan-500 text-cyan-500 px-8 py-3 rounded-full font-semibold text-sm hover:bg-cyan-500 hover:text-white transition-all duration-300 shadow-sm hover:shadow-md"
                          >
                      www.medixbuy.com  
                </a>
                    </div>
                    
                  </div>
                       
                  {/* Content placeholder
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
                    <div className="text-gray-400 mb-4">
                      <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold font-roboto text-gray-600 mb-2">
                      {getContent('contentComingSoon')}
                    </h3>
                    <p className="text-gray-500">
                      {getContent('detailedInfo')} {services.find(s => s.id === activeService)?.sub} {getContent('willBeAdded')}
                    </p>
                  </div> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
