import SEO from '@/components/SEO';
import { getSEOConfig } from '@/lib/seo-config';

export default async function AboutPage({
  params
}: {
  params: Promise<{locale: string}>;
}) {
  const {locale} = await params;
  const seoConfig = getSEOConfig(locale, 'about');

  // Get page content based on locale
  const getContent = (key: string) => {
    const content: Record<string, any> = {
      en: {
        // Our Values Section
        valuesTitle: 'Our Values',
        valuesParagraph1: 'Since our founding, our main goal has been customer satisfaction — offering each client a unique, tailored service by actively listening to their needs and supporting them throughout the entire process.',
        valuesParagraph2: 'At Riomavix, we believe in building relationships rooted in trust, transparency, and commitment. We act with responsibility in every task, prioritizing quality, ethical business practices, and respect for our clients and partners. Our mindset is driven by continuous improvement, agility, and collaboration — enabling us to quickly adapt to the evolving needs of the global market.',

        // Our Team Section
        teamTitle: 'Our    Team',
        europeTeamTitle: 'EUROPE TEAM (HEADQUARTERS IN MADRID, SPAIN)',
        shanghaiTeamTitle: 'SHANGHAI OFFICE (CHINA)',

        // Team Members
        celiaName: '<PERSON>',
        celiaPosition: 'Project Manager for the EU Market',
        celiaEmail: '<EMAIL>',
        celiaPhone: '+34 680 55 86 59',

        laraPampinName: 'Lara Pampin',
        laraPampinPosition: 'Data analyst',
        laraPampinEmail: '<EMAIL>',

        rubenName: 'Rubén Molina',
        rubenPosition: 'Data analyst',
        rubenEmail: '<EMAIL>',

        joseMariaName: 'José María Pérez',
        joseMariaPosition: 'Person Responsible for Regulatory Compliance (PRRC)',

        leiShiName: 'Lei Shi',
        leiShiPosition: 'Director',
        leiShiEmail: '<EMAIL>',
        leiShiWechat: 'RioMachina',

        galileoName: 'Galileo Han',
        galileoPosition: 'Compliance Manager',
        galileoEmail: '<EMAIL>',

        chenjiFengName: 'Chenji Feng',
        chenjiFengPosition: 'Marketing Manager',
        chenjiFengEmail: '<EMAIL>'
      },
      zh: {
        // Our Values Section
        valuesTitle: '我们的价值观',
        valuesParagraph1: '自成立以来，客户满意始终是我们的首要目标。我们提供专属定制服务，倾听客户需求并全程协作。',
        valuesParagraph2: '在Riomavix，我们秉持信任、透明与承诺构建合作关系。每项管理均遵循责任原则，优先保障质量、商业道德及对合作伙伴与客户的尊重。我们致力于持续改进、快速响应及协作思维，以适应全球市场的动态需求。',

        // Our Team Section
        teamTitle: '我们的团队',
        europeTeamTitle: '欧洲团队（总部位于西班牙马德里）',
        shanghaiTeamTitle: '上海办公室（中国）',

        // Team Members
        celiaName: 'Celia de Diego',
        celiaPosition: '欧盟项目经理',
        celiaEmail: '<EMAIL>',
        celiaPhone: '+34 680 55 86 59',

        laraPampinName: 'Lara Pampin',
        laraPampinPosition: '数据分析师',
        laraPampinEmail: '<EMAIL>',

        rubenName: 'Rubén Molina',
        rubenPosition: '数据分析师',
        rubenEmail: '<EMAIL>',

        joseMariaName: 'José María Pérez',
        joseMariaPosition: '法规合规负责人（PRRC）',

        leiShiName: 'Lei Shi',
        leiShiPosition: '总监',
        leiShiEmail: '<EMAIL>',
        leiShiWechat: 'RioMachina',

        galileoName: 'Galileo Han',
        galileoPosition: '法规合规负责人',
        galileoEmail: '<EMAIL>',

        chenjiFengName: 'Chenji Feng',
        chenjiFengPosition: '市场经理',
        chenjiFengEmail: '<EMAIL>'
      },
      es: {
        // Our Values Section
        valuesTitle: 'Nuestros Valores',
        valuesParagraph1: 'Desde nuestros inicios, nuestro principal objetivo ha sido la satisfacción del cliente, ofreciendo un servicio exclusivo, personalizado y guiado por la escucha activa de sus necesidades a lo largo de todo el proceso.',
        valuesParagraph2: 'En Riomavix, creemos en relaciones basadas en la confianza, la transparencia y el compromiso. Actuamos con responsabilidad en cada gestión, priorizando la calidad, la ética empresarial y el respeto hacia nuestros socios y clientes. Nuestra filosofía se centra en la mejora continua, la agilidad en la respuesta y una mentalidad colaborativa que nos permite adaptarnos a los constantes cambios del mercado global.',

        // Our Team Section
        teamTitle: 'Conoce a Nuestro Equipo',
        europeTeamTitle: 'EQUIPO EUROPEO (SEDE EN MADRID, ESPAÑA)',
        shanghaiTeamTitle: 'OFICINA DE SHANGHAI (CHINA)',

        // Team Members
        celiaName: 'Celia de Diego',
        celiaPosition:'Project Manager para el Mercado de la UE',
        celiaEmail: '<EMAIL>',
        celiaPhone: '+34 680 55 86 59',

        laraPampinName: 'Lara Pampin',
        laraPampinPosition: 'Analista de datos',
        laraPampinEmail: '<EMAIL>',

        rubenName: 'Rubén Molina',
        rubenPosition: 'Analista de datos',
        rubenEmail: '<EMAIL>',

        joseMariaName: 'José María Pérez',
        joseMariaPosition: 'Persona Responsable del Cumplimiento Regulatorio (PRRC)',

        leiShiName: 'Lei Shi',
        leiShiPosition: 'Director',
        leiShiEmail: '<EMAIL>',
        leiShiWechat: 'RioMachina',

        galileoName: 'Galileo Han',
        galileoPosition: 'Responsable de Cumplimiento de normativa',
        galileoEmail: '<EMAIL>',

        chenjiFengName: 'Chenji Feng',
        chenjiFengPosition: 'Gerente de Marketing',
        chenjiFengEmail: '<EMAIL>'
      }
    };
    return content[locale]?.[key] || content.en[key];
  };

  return (
    <div className="min-h-screen">
      <SEO
        title={seoConfig.title}
        description={seoConfig.description}
        keywords={seoConfig.keywords}
        locale={locale}
        path={`/${locale}/about`}
      />
      {/* Our Values Section */}
      <section className="bg-primary text-white py-10">
        <div className="container mx-auto px-6 lg:px-12">
          <h1 className="text-4xl md:text-5xl font-black font-roboto text-center">
            {getContent('valuesTitle')}
          </h1>
        </div>
      </section>

      {/* Values Content */}
      <section className="bg-gray-50 py-16 mb-20">
        <div className="container mx-auto px-6 lg:px-12">
          <div className="max-w-4xl mx-auto space-y-6 text-gray-700 text-lg leading-relaxed">
            <p>
              {getContent('valuesParagraph1')}
            </p>
            <p>
              {getContent('valuesParagraph2')}
            </p>
          </div>
        </div>
      </section>

      {/* Our Team Section */}
      <section className="bg-primary text-white py-10">
        <div className="container mx-auto px-6 lg:px-12">
          <h2 className="text-4xl md:text-5xl font-black font-roboto text-center">
            {getContent('teamTitle')}
          </h2>
        </div>
      </section>

      {/* Team Content */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6 lg:px-12">
          {/* Europe Team */}
          <div className="mb-16">
            <h3 className="text-cyan-500 text-lg font-bold font-roboto tracking-wide mb-6 text-left">
              {getContent('europeTeamTitle')}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
              {/* Celia de Diego */}
              <div className="text-left">
                <div className="flex items-center mb-2">
                  <h4 className="text-xl font-black font-roboto text-primary mr-2">
                    {getContent('celiaName')}
                  </h4>
                  <div className="w-6 h-6 bg-gray-300 rounded flex items-center justify-center">
                    <a href="https://www.linkedin.com/in/celia-de-diego-763788285/"  target="_blank"
              rel="noopener noreferrer" className="text-white text-xs font-bold">in</a>
                  </div>
                </div>
                    <p className="text-gray-400 italic text-sm mb-1 font-bold">
                  {getContent('celiaPosition')}
                </p>
                <p className="text-gray-700 text-sm mb-1">
                  E-mail: {getContent('celiaEmail')}
                </p>
                <p className="text-gray-700 text-sm">
                  Phone: {getContent('celiaPhone')}
                </p>
              </div>

              {/* Lara Pampin */}
              <div className="text-left">
                <div className="flex items-center mb-2">
                  <h4 className="text-xl font-black font-roboto text-primary mr-2">
                    {getContent('laraPampinName')}
                  </h4>
                  <div className="w-6 h-6 bg-gray-300 rounded flex items-center justify-center">
                    <a href="https://www.linkedin.com/in/lara-pampin/"  target="_blank"
              rel="noopener noreferrer" className="text-white text-xs font-bold">in</a>
                  </div>
                </div>
                    <p className="text-gray-400 italic text-sm mb-1 font-bold">
                  {getContent('laraPampinPosition')}
                </p>
                <p className="text-gray-700 text-sm">
                  E-mail: {getContent('laraPampinEmail')}
                </p>
              </div>

              {/* Rubén Molina */}
              <div className="text-left">
                <div className="flex items-center mb-2">
                  <h4 className="text-xl font-black font-roboto text-primary mr-2">
                    {getContent('rubenName')}
                  </h4>
                  <div className="w-6 h-6 bg-gray-300 rounded flex items-center justify-center">
                    <a href="https://www.linkedin.com/in/rub%C3%A9nmolina/"  target="_blank" rel="noopener noreferrer" className="text-white text-xs font-bold">in</a>
                  </div>
                </div>
                  <p className="text-gray-400 italic text-sm mb-1 font-bold">
                  {getContent('rubenPosition')}
                </p>
                <p className="text-gray-700 text-sm">
                  E-mail: {getContent('rubenEmail')}
                </p>
              </div>

              {/* José María Pérez */}
              <div className="text-left">
                <h4 className="text-xl font-black font-roboto text-primary mb-2">
                  {getContent('joseMariaName')}
                </h4>
                     <p className="text-gray-400 italic text-sm mb-1 font-bold">
                  {getContent('joseMariaPosition')}
                </p>
              </div>
            </div>
          </div>

          {/* Shanghai Office */}
          <div>
            <h3 className="text-cyan-500 text-lg font-black font-roboto tracking-wide mb-6 text-left">
              {getContent('shanghaiTeamTitle')}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Lei Shi */}
              <div className="text-left">
                <h4 className="text-xl font-black font-roboto text-primary mb-2">
                  {getContent('leiShiName')}
                </h4>
                    <p className="text-gray-400 italic text-sm mb-1 font-bold">
                  {getContent('leiShiPosition')}
                </p>
                <p className="text-gray-700 text-sm mb-1">
                  E-mail: {getContent('leiShiEmail')}
                </p>
                <p className="text-gray-700 text-sm">
                  Wechat: {getContent('leiShiWechat')}
                </p>
              </div>

              {/* Galileo Han */}
              <div className="text-left">
                <h4 className="text-xl font-black font-roboto text-primary mb-2">
                  {getContent('galileoName')}
                </h4>
                <p className="text-gray-400 italic text-sm mb-1 font-bold">
                  {getContent('galileoPosition')}
                </p>
                <p className="text-gray-700 text-sm">
                  E-mail: {getContent('galileoEmail')}
                </p>
              </div>

              {/* Chenji Feng */}
              <div className="text-left">
                <h4 className="text-xl font-black font-roboto text-primary mb-2">
                  {getContent('chenjiFengName')}
                </h4>
                <p className="text-gray-400 italic text-sm mb-1 font-bold">
                  {getContent('chenjiFengPosition')}
                </p>
                <p className="text-gray-700 text-sm">
                  E-mail: {getContent('chenjiFengEmail')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
