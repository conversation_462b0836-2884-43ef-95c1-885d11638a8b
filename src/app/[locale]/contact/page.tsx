'use client';

import { useState, use } from 'react';
import SEO from '@/components/SEO';
import { getSEOConfig } from '@/lib/seo-config';

interface FormData {
  name: string;
  email: string;
  company: string;
  requirements: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  company?: string;
  requirements?: string;
}

export default function ContactPage({
  params
}: {
  params: Promise<{locale: string}>;
}) {
  const {locale} = use(params);
  const seoConfig = getSEOConfig(locale, 'contact');

  // 表单状态
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    company: '',
    requirements: ''
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // 验证姓名
    if (!formData.name.trim()) {
      newErrors.name = getValidationMessage('nameRequired');
    } else if (formData.name.length > 50) {
      newErrors.name = getValidationMessage('nameMaxLength');
    }

    // 验证邮箱
    if (!formData.email.trim()) {
      newErrors.email = getValidationMessage('emailRequired');
    } else if (formData.email.length > 50) {
      newErrors.email = getValidationMessage('emailMaxLength');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = getValidationMessage('emailInvalid');
    }

    // 验证公司
    if (!formData.company.trim()) {
      newErrors.company = getValidationMessage('companyRequired');
    } else if (formData.company.length > 50) {
      newErrors.company = getValidationMessage('companyMaxLength');
    }

    // 验证需求
    if (!formData.requirements.trim()) {
      newErrors.requirements = getValidationMessage('requirementsRequired');
    } else if (formData.requirements.length > 500) {
      newErrors.requirements = getValidationMessage('requirementsMaxLength');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          locale
        }),
      });

      if (response.ok) {
        setShowSuccess(true);
        setFormData({
          name: '',
          email: '',
          company: '',
          requirements: ''
        });
        setErrors({});

        // 禁用按钮1分钟
        setIsDisabled(true);
        setTimeout(() => {
          setIsDisabled(false);
        }, 60000);

        // 3秒后隐藏成功消息
        setTimeout(() => {
          setShowSuccess(false);
        }, 3000);
      } else {
        const errorData = await response.json();
        console.error('Error sending email:', errorData);
        alert(getValidationMessage('sendError'));
      }
    } catch (error) {
      console.error('Error sending email:', error);
      alert(getValidationMessage('sendError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // 清除对应字段的错误
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  // 获取验证消息
  const getValidationMessage = (key: string) => {
    const messages: Record<string, any> = {
      en: {
        nameRequired: 'Name is required',
        nameMaxLength: 'Name must be 50 characters or less',
        emailRequired: 'Email is required',
        emailMaxLength: 'Email must be 50 characters or less',
        emailInvalid: 'Please enter a valid email address',
        companyRequired: 'Company is required',
        companyMaxLength: 'Company must be 50 characters or less',
        requirementsRequired: 'Requirements are required',
        requirementsMaxLength: 'Requirements must be 500 characters or less',
        sendError: 'Failed to send message. Please try again.',
        sendSuccess: 'Message sent successfully!'
      },
      zh: {
        nameRequired: '姓名为必填项',
        nameMaxLength: '姓名不能超过50个字符',
        emailRequired: '邮箱为必填项',
        emailMaxLength: '邮箱不能超过50个字符',
        emailInvalid: '请输入有效的邮箱地址',
        companyRequired: '公司为必填项',
        companyMaxLength: '公司名称不能超过50个字符',
        requirementsRequired: '需求说明为必填项',
        requirementsMaxLength: '需求说明不能超过500个字符',
        sendError: '发送失败，请重试。',
        sendSuccess: '发送成功！'
      },
      es: {
        nameRequired: 'El nombre es obligatorio',
        nameMaxLength: 'El nombre debe tener 50 caracteres o menos',
        emailRequired: 'El correo es obligatorio',
        emailMaxLength: 'El correo debe tener 50 caracteres o menos',
        emailInvalid: 'Por favor ingrese un correo válido',
        companyRequired: 'La empresa es obligatoria',
        companyMaxLength: 'La empresa debe tener 50 caracteres o menos',
        requirementsRequired: 'Los requisitos son obligatorios',
        requirementsMaxLength: 'Los requisitos deben tener 500 caracteres o menos',
        sendError: 'Error al enviar. Por favor intente de nuevo.',
        sendSuccess: '¡Mensaje enviado exitosamente!'
      }
    };
    return messages[locale]?.[key] || messages.en[key];
  };

  // Get page content based on locale
  const getContent = (key: string) => {
    const content: Record<string, any> = {
      en: {
        // Header
        title: 'Contact Us',
        companyName: 'Riomavix',

        // Europe Team
        europeTeamTitle: 'EUROPE TEAM (HEADQUARTERS IN MADRID, SPAIN)',
        addressLabel: 'Address:',
        phoneLabel: 'Phone:',
        emailLabel: 'E-mail:',

        europeAddress1: 'Calle de Goya, 21, 1D, Madrid, Spain',
        europeAddress2: 'Calle Almansa 55, 1D, Madrid Spain',
        europePhone: '+34 680 55 86 59',
        europeEmail: '<EMAIL>',

        // Shanghai Office
        shanghaiTitle: 'SHANGHAI OFFICE (CHINA)',
        shanghaiAddress: 'Calle LongCao Lane 1, Edif 7, 401a, Shanghai, PRC',
        shanghaiPhone: '+86 021 6457 6674',
        shanghaiEmail: '<EMAIL>',

        // Contact Form
        contactFormTitle: 'Contact From:',
        nameLabel: 'Your Name',
        emailFormLabel: 'Your Email',
        companyLabel: 'Your Company',
        requirementsLabel: 'Please let us know your requirements.',
        sendButton: 'Send'
      },
      zh: {
        // Header
        title: '联系我们',
        companyName: 'Riomavix',

        // Europe Team
        europeTeamTitle: '欧洲团队（马德里总部）',
        addressLabel: '地址：',
        phoneLabel: '电话：',
        emailLabel: '邮箱：',

        europeAddress1: 'Calle de Goya, 21, 1D, Madrid, España',
        europeAddress2: 'Calle Almansa 55, 1D, Madrid España',
        europePhone: '+34 680 55 86 59',
        europeEmail: '<EMAIL>',

        // Shanghai Office
        shanghaiTitle: '上海办公室（中国）',
        shanghaiAddress: '上海市徐汇区龙漕路1弄7号401A室',
        shanghaiPhone: '+86 021 6457 6674',
        shanghaiEmail: '<EMAIL>',

        // Contact Form
        contactFormTitle: '联系表单：',
        nameLabel: '您的姓名',
        emailFormLabel: '您的邮箱',
        companyLabel: '您的公司',
        requirementsLabel: '请说明您的需求。',
        sendButton: '提交'
      },
      es: {
        // Header
        title: 'Contáctanos',
        companyName: 'Riomavix',

        // Europe Team
        europeTeamTitle: 'EQUIPO EUROPEO (Sede en Madrid, España)',
        addressLabel: 'Dirección:',
        phoneLabel: 'Teléfono:',
        emailLabel: 'Correo:',

        europeAddress1: 'Calle de Goya, 21, 1D, Madrid, España',
        europeAddress2: 'Calle Almansa 55, 1D, Madrid España',
        europePhone: '+34 680 55 86 59',
        europeEmail: '<EMAIL>',

        // Shanghai Office
        shanghaiTitle: 'OFICINA DE SHANGHAI (CHINA)',
        shanghaiAddress: 'Calle LongCao Lane 1, Edif 7, 401a,Shanghai, PRC',
        shanghaiPhone: '+86 021 6457 6674',
        shanghaiEmail: '<EMAIL>',

        // Contact Form
        contactFormTitle: 'Formulario de Contacto:',
        nameLabel: 'Tu nombre',
        emailFormLabel: 'Tu correo',
        companyLabel: 'Tu empresa',
        requirementsLabel: 'Por favor, háganos saber sus requisitos',
        sendButton: 'Enviar'
      }
    };
    return content[locale]?.[key] || content.en[key];
  };

  return (
    <div className="min-h-screen">
      <SEO
        title={seoConfig.title}
        description={seoConfig.description}
        keywords={seoConfig.keywords}
        locale={locale}
        path={`/${locale}/contact`}
      />
      {/* Header Section */}
      <section className="bg-primary text-white py-10">
        <div className="container mx-auto px-6 lg:px-12">
          <h1 className="text-4xl md:text-5xl font-black font-roboto text-center">
            {getContent('title')}
          </h1>
        </div>
      </section>

      {/* Content Section */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6 lg:px-12">
          {/* Company Name */}
          <h2 className="text-4xl font-black font-roboto text-primary mb-6">
            {getContent('companyName')}
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Left Column - Contact Information */}
            <div className="space-y-12">
              {/* Europe Team */}
              <div>
                <h3 className="text-secondary text-lg font-bold font-roboto tracking-wide mb-5">
                  {getContent('europeTeamTitle')}
                </h3>

                <div className="space-y-4">
                  <div className="">
                    <span className="font-semibold text-gray-900">{getContent('addressLabel')}</span>
                      <span className="text-gray-700 ml-4">{getContent('europeAddress1')}</span>
                    <div className="text-gray-700 ml-4">
                      <p className="ml-18">{getContent('europeAddress2')}</p>
                    </div>
                  </div>

                  <div>
                    <span className="font-semibold text-gray-900">{getContent('phoneLabel')}</span>
                    <span className="text-gray-700 ml-4">{getContent('europePhone')}</span>
                  </div>

                  <div>
                    <span className="font-semibold text-gray-900">{getContent('emailLabel')}</span>
                    <span className="text-gray-700 ml-4">{getContent('europeEmail')}</span>
                  </div>
                </div>
              </div>

              {/* Shanghai Office */}
              <div>
                <h3 className="text-cyan-500 text-lg font-bold font-roboto tracking-wide mb-5">
                  {getContent('shanghaiTitle')}
                </h3>

                <div className="space-y-4">
                  <div>
                    <span className="font-semibold text-gray-900">{getContent('addressLabel')}</span>
                    <span className="text-gray-700 ml-4">{getContent('shanghaiAddress')}</span>
                  </div>

                  <div>
                    <span className="font-semibold text-gray-900">{getContent('phoneLabel')}</span>
                    <span className="text-gray-700 ml-4">{getContent('shanghaiPhone')}</span>
                  </div>

                  <div>
                    <span className="font-semibold text-gray-900">{getContent('emailLabel')}</span>
                    <span className="text-gray-700 ml-4">{getContent('shanghaiEmail')}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Contact Form */}
            <div className="bg-gray-100 rounded-lg p-8 relative">
              <h3 className="text-lg font-bold  font-roboto text-gray-900 mb-6">
                {getContent('contactFormTitle')}
              </h3>

              {/* 成功提示 */}
              {showSuccess && (
                <div className="absolute top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-10">
                  {getValidationMessage('sendSuccess')}
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder={getContent('nameLabel')}
                    maxLength={50}
                    className={`w-full px-4 py-2 border-b bg-transparent focus:outline-none placeholder-gray-600 ${
                      errors.name ? 'border-red-500 focus:border-red-500' : 'border-gray-400 focus:border-blue-600'
                    }`}
                  />
                  {errors.name && (
                    <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                  )}
                </div>

                <div>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder={getContent('emailFormLabel')}
                    maxLength={50}
                    className={`w-full px-4 py-2 border-b bg-transparent focus:outline-none placeholder-gray-600 ${
                      errors.email ? 'border-red-500 focus:border-red-500' : 'border-gray-400 focus:border-blue-600'
                    }`}
                  />
                  {errors.email && (
                    <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                  )}
                </div>

                <div>
                  <input
                    type="text"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    placeholder={getContent('companyLabel')}
                    maxLength={50}
                    className={`w-full px-4 py-2 border-b bg-transparent focus:outline-none placeholder-gray-600 ${
                      errors.company ? 'border-red-500 focus:border-red-500' : 'border-gray-400 focus:border-blue-600'
                    }`}
                  />
                  {errors.company && (
                    <p className="text-red-500 text-sm mt-1">{errors.company}</p>
                  )}
                </div>

                <div className="pt-4">
                  <label className="block text-gray-900 font-medium mb-4">
                    {getContent('requirementsLabel')}
                  </label>
                  <textarea
                    name="requirements"
                    value={formData.requirements}
                    onChange={handleInputChange}
                    rows={6}
                    maxLength={500}
                    className={`w-full px-4 py-3 border rounded bg-white focus:outline-none resize-none ${
                      errors.requirements ? 'border-red-500 focus:border-red-500' : 'border-gray-400 focus:border-blue-600'
                    }`}
                    placeholder=""
                  />
                  <div className="flex justify-between items-center mt-1">
                    <div>
                      {errors.requirements && (
                        <p className="text-red-500 text-sm">{errors.requirements}</p>
                      )}
                    </div>
                    <p className="text-gray-500 text-sm">
                      {formData.requirements.length}/500
                    </p>
                  </div>
                </div>

                <div className="flex justify-center pt-6">
                  <button
                    type="submit"
                    disabled={isSubmitting || isDisabled}
                    className={`px-12 py-3 rounded-full font-semibold transition-colors ${
                      isSubmitting || isDisabled
                        ? 'bg-gray-300 border-2 border-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-white border-2 border-gray-400 text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {locale === 'zh' ? '发送中...' : locale === 'es' ? 'Enviando...' : 'Sending...'}
                      </span>
                    ) : isDisabled ? (
                      locale === 'zh' ? '请等待 1 分钟' : locale === 'es' ? 'Espere 1 minuto' : 'Wait 1 minute'
                    ) : (
                      getContent('sendButton')
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
