import SEO from '@/components/SEO';
import { getSEOConfig } from '@/lib/seo-config';

export default async function GPSRPage({
  params
}: {
  params: Promise<{locale: string}>;
}) {
  const {locale} = await params;
  const seoConfig = getSEOConfig(locale, 'gpsr');

  // Get page content based on locale
  const getContent = (key: string) => {
    const content: Record<string, any> = {
      en: {
        title: 'GPSR',
        subtitle: 'General Product Safety Regulation',
        articleTitle: 'Core Interpretation of the EU General Product Safety Regulation (GPSR)',
        section1: {
          title: '1. Regulatory Overview and Core Mission',
          content: [
            'The European Union\'s General Product Safety Regulation (GPSR) is a critical legal framework whose core mission is to establish unified and stringent safety standards for all consumer products entering the EU market. This regulation applies to a wide range of consumer goods but explicitly excludes categories already regulated by specific legislation, such as food, pharmaceuticals, cosmetics, and medical devices. Whether sold through traditional trade channels or online e-commerce platforms to EU consumers, all products must strictly comply with GPSR requirements.',
            'GPSR aims to achieve several strategic objectives:',
            '• Establish Higher Safety Standards: The regulation\'s primary task is to ensure the safety of non-food consumer products sold within the EU, fundamentally protecting consumer health and personal safety.',
            '• Strengthen Supply Chain Traceability: By mandating businesses to provide more complete and transparent product origin and distribution information, regulatory authorities can more quickly locate and trace products with safety hazards, enabling efficient market intervention or recalls.',
            '• Upgrade Consumer Rights Protection: The regulation requires detailed product information and compliance certification, giving consumers more comprehensive right to information, enabling them to make safe purchasing decisions based on reliable information.',
            '• Fill Digital Regulatory Gaps: With the rise of e-commerce and new distribution models, GPSR aims to fill areas not covered by traditional regulation, ensuring product safety standards can adapt to rapidly changing market environments.',
            '• Create a Fair Market Environment: By implementing unified safety and compliance standards, GPSR sets a fair competitive baseline for all market participants, effectively curbing unfair competition practices.'
          ]
        },
        section2: {
          title: '2. Regulatory Scope and Effective Date',
          content: [
            'The new GPSR regulation was officially promulgated on May 23, 2023, and will be mandatory from December 13, 2024.',
            'The regulation has an extremely broad coverage, mainly targeting consumer products not governed by other specific EU regulations (such as cosmetics regulations, medical device regulations, etc.). The applicable industries and product categories include but are not limited to:',
            '• Electronic and Electrical Products: Such as household appliances, consumer electronics, and accessories.',
            '• Children\'s Products and Toys: Setting extremely high safety thresholds for products used by children.',
            '• Furniture and Home Products: Including furniture, interior decorations, and various daily necessities.',
            '• Textiles and Clothing: Requiring clothing, bedding, etc., to be free of harmful substances.',
            '• DIY, Gardening, and Automotive Consumer Products: Such as tools, equipment, child safety seats, and automotive accessories.',
            '• Sports and Leisure Equipment: Various equipment used for sports and entertainment activities.',
            '• Food Contact Materials: Such as cups, straws, food packaging, etc.',
            '• Other Products Not Covered by Specific Regulations: Such as candles, aromatherapy, jewelry, glue, leather care products, etc.',
            'It is worth noting that GPSR\'s regulatory scope is not limited to new products but also includes second-hand, repaired, and refurbished products.'
          ]
        },
        section3: {
          title: '3. Major Changes Compared to Previous Regulations',
          content: [
            'GPSR has significantly upgraded the EU\'s existing safety requirements in multiple aspects, particularly emphasizing responsibilities at the front end of the product lifecycle.',
            '• Deepened Risk Assessment: GPSR requires companies to conduct more comprehensive and detailed risk assessments, covering not only traditional risks but also proactively considering potential safety hazards arising from new technology applications.',
            '• Strengthened Technical Documentation: The regulation\'s requirements for technical documentation have become more stringent and detailed. Companies must not only prove their product compliance but also record in detail the risk assessment process and safety measures taken.',
            '• Front-loaded Safety Responsibility: GPSR emphasizes that manufacturers must deeply integrate safety functions at the product design stage, rather than merely meeting basic compliance standards.',
            '• Upgraded Traceability: Implementing a more stringent full-chain traceability system, ensuring every link from production to consumption is clearly traceable, providing support for efficient market supervision and product recalls.',
            '• Coordinated Market Supervision: Promoting more proactive cooperation between companies and market regulatory authorities, shifting from "passive response to problems" to "proactive risk prevention".'
          ]
        },
        section4: {
          title: '4. Compliance Obligations for Different Market Roles',
          content: [
            'GPSR sets clear legal responsibilities for each participant in the supply chain (manufacturers, importers, distributors).',
            '• Manufacturer Obligations:',
            'As the primary responsible party for product safety, manufacturers must ensure product safety throughout the design and production process. This includes conducting thorough risk assessments, establishing and maintaining complete technical documentation, ensuring product traceability, and affixing compliance marks such as CE when necessary. After products enter the market, any safety incidents must be immediately reported to competent authorities and effective recall procedures must be initiated.',
            '• Importer Obligations:',
            'If the manufacturer is located outside the EU, the above responsibilities transfer to importers within the EU. Importers are responsible for strictly verifying that manufacturers have fulfilled all compliance procedures before placing products on the market and ensuring complete technical documentation. Additionally, importers must maintain compliance records, conduct sampling inspections of imported products, and bear responsibility for reporting safety issues.',
            '• Distributor Obligations:',
            'Before distributing products, distributors must conduct verification to ensure products bear necessary compliance marks (such as CE marks) and safety instructions. During distribution, they are responsible for monitoring product safety. Once problems are discovered or incident reports are received, they must promptly notify manufacturers or importers and relevant authorities, and actively assist in recall efforts.'
          ]
        }
      },
      zh: {
        title: 'GPSR',
        subtitle: '通用产品安全法规',
        articleTitle: '欧盟《通用产品安全法规》(GPSR)核心解读',
        section1: {
          title: '一、法规概述与核心宗旨',
          content: [
            '欧盟的《通用产品安全法规》（GPSR）是一项关键性的法律框架，其核心使命是为进入欧盟市场的所有消费品设立统一且严格的安全标准。该法规适用于广大消费品类，但明确排除了已有专门法规监管的类别，如食品、药品、化妆品及医疗设备。无论是通过传统贸易渠道还是经由线上电商平台销售给欧盟消费者的产品，都必须严格遵守GPSR的规定。',
            'GPSR的设立旨在实现多个战略目标：',
            '• 确立更高的安全基准：法规的首要任务是保障在欧盟境内销售的非食品类消费品的安全性，从而根本上保护消费者的健康与人身安全。',
            '• 强化供应链追溯能力：通过强制要求商家提供更完整、透明的产品来源与流向信息，监管机构能够更迅速地定位并追溯存在安全隐患的产品，进而高效执行市场干预或召回。',
            '• 升级消费者权益保护：法规要求提供详尽的产品信息与合规证明，赋予了消费者更充分的知情权，使他们能基于可靠信息做出安全的购买选择。',
            '• 弥补数字化监管空白：随着电子商务和新型分销模式的崛起，GPSR旨在填补传统监管未能覆盖的领域，确保产品安全标准能适应快速变化的市场环境。',
            '• 营造公平的市场环境：通过施行统一的安全与合规标准，GPSR为所有市场参与者设定了公平的竞争底线，有效遏制了不公平竞争行为。'
          ]
        },
        section2: {
          title: '二、法规适用范围与生效日期',
          content: [
            '全新的GPSR法规已于2023年5月23日正式颁布，并将从2024年12月13日起强制执行。',
            '该法规覆盖范围极广，主要针对那些没有被欧盟其他特定法规（如化妆品法规、医疗器械法规等）所管辖的消费品。其适用的行业与产品类别包括但不限于：',
            '• 电子电器产品：如家用电器、消费电子产品及配件。',
            '• 儿童用品与玩具：对儿童使用的产品设定了极高的安全门槛。',
            '• 家具与家居用品：包括家具、室内装饰品及各类日用杂货。',
            '• 纺织品与服装：要求衣物、床上用品等不含有害物质。',
            '• DIY、园艺及汽车相关消费品：如工具、设备、儿童安全座椅及汽车配件。',
            '• 体育与休闲装备：用于运动和娱乐活动的各类器材。',
            '• 食品接触材料：例如水杯、吸管、食品包装等。',
            '• 其他未被特定法规覆盖的产品：如蜡烛、香薰、珠宝首饰、胶水、皮革护理品等。',
            '值得注意的是，GPSR的监管对象不仅限于新产品，同样也包含了二手、维修过以及翻新的产品。'
          ]
        },
        section3: {
          title: '三、相较于过往法规的主要变化',
          content: [
            'GPSR在多个方面对欧盟现行的安全要求进行了显著的升级，尤其强调了在产品生命周期前端的责任。',
            '• 风险评估的深化：GPSR要求企业进行更全面、更细致的风险评估，不仅覆盖传统风险，还必须前瞻性地考虑因新技术应用而产生的潜在安全隐患。',
            '• 技术文档的强化：法规对技术文档的要求变得更为严格和详尽，企业不仅要证明其产品合规，还需详细记录风险评估的过程与所采取的安全措施。',
            '• 安全责任的前置：GPSR强调制造商必须在产品设计阶段就将安全功能深度整合，而非仅仅满足基本的合规标准。',
            '• 可追溯性的升级：实施了更严苛的全链条追溯体系，确保从生产到消费的每个环节都清晰可查，为高效的市场监管和产品召回提供支持。',
            '• 市场监督的协同：推动企业与市场监管机构之间更主动的合作，从"被动响应问题"转变为"主动预防风险"。'
          ]
        },
        section4: {
          title: '四、不同市场角色的合规义务',
          content: [
            'GPSR为供应链上的各个参与者（制造商、进口商、分销商）都设定了明确的法律责任。',
            '• 制造商的义务：',
            '作为产品安全的第一责任人，制造商必须在设计和生产全流程中确保产品安全。这包括执行彻底的风险评估、建立并维护完整的技术文档、确保产品的可追溯性，并在需要时加贴CE等合规标志。产品上市后，一旦发生安全事故，必须立即向主管机构报告并启动有效的召回程序。',
            '• 进口商的义务：',
            '若制造商位于欧盟境外，上述责任则顺移至欧盟境内的进口商。进口商有责任在产品投放市场前，严格验证制造商是否已履行所有合规程序，并确保相关技术文件齐全。同时，进口商需要保存合规记录、对进口产品进行抽样检查，并承担报告安全问题的责任。',
            '• 经销商的义务：',
            '分销商在分销产品前，必须进行核查，确保产品带有必要的合规标志（如CE标志）和安全说明。在分销过程中，他们有责任监控产品的安全性，一旦发现问题或收到事故报告，需及时通报给制造商或进口商以及相关当局，并积极协助召回工作。'
          ]
        }
      },
      es: {
        title: 'GPSR',
        subtitle: 'General Product Safety Regulation',
        articleTitle: 'Core Interpretation of the EU General Product Safety Regulation (GPSR)',
        section1: {
          title: '1. Regulatory Overview and Core Mission',
          content: [
            'The European Union\'s General Product Safety Regulation (GPSR) is a critical legal framework whose core mission is to establish unified and stringent safety standards for all consumer products entering the EU market. This regulation applies to a wide range of consumer goods but explicitly excludes categories already regulated by specific legislation, such as food, pharmaceuticals, cosmetics, and medical devices. Whether sold through traditional trade channels or online e-commerce platforms to EU consumers, all products must strictly comply with GPSR requirements.',
            'GPSR aims to achieve several strategic objectives:',
            '• Establish Higher Safety Standards: The regulation\'s primary task is to ensure the safety of non-food consumer products sold within the EU, fundamentally protecting consumer health and personal safety.',
            '• Strengthen Supply Chain Traceability: By mandating businesses to provide more complete and transparent product origin and distribution information, regulatory authorities can more quickly locate and trace products with safety hazards, enabling efficient market intervention or recalls.',
            '• Upgrade Consumer Rights Protection: The regulation requires detailed product information and compliance certification, giving consumers more comprehensive right to information, enabling them to make safe purchasing decisions based on reliable information.',
            '• Fill Digital Regulatory Gaps: With the rise of e-commerce and new distribution models, GPSR aims to fill areas not covered by traditional regulation, ensuring product safety standards can adapt to rapidly changing market environments.',
            '• Create a Fair Market Environment: By implementing unified safety and compliance standards, GPSR sets a fair competitive baseline for all market participants, effectively curbing unfair competition practices.'
          ]
        },
        section2: {
          title: '2. Regulatory Scope and Effective Date',
          content: [
            'The new GPSR regulation was officially promulgated on May 23, 2023, and will be mandatory from December 13, 2024.',
            'The regulation has an extremely broad coverage, mainly targeting consumer products not governed by other specific EU regulations (such as cosmetics regulations, medical device regulations, etc.). The applicable industries and product categories include but are not limited to:',
            '• Electronic and Electrical Products: Such as household appliances, consumer electronics, and accessories.',
            '• Children\'s Products and Toys: Setting extremely high safety thresholds for products used by children.',
            '• Furniture and Home Products: Including furniture, interior decorations, and various daily necessities.',
            '• Textiles and Clothing: Requiring clothing, bedding, etc., to be free of harmful substances.',
            '• DIY, Gardening, and Automotive Consumer Products: Such as tools, equipment, child safety seats, and automotive accessories.',
            '• Sports and Leisure Equipment: Various equipment used for sports and entertainment activities.',
            '• Food Contact Materials: Such as cups, straws, food packaging, etc.',
            '• Other Products Not Covered by Specific Regulations: Such as candles, aromatherapy, jewelry, glue, leather care products, etc.',
            'It is worth noting that GPSR\'s regulatory scope is not limited to new products but also includes second-hand, repaired, and refurbished products.'
          ]
        },
        section3: {
          title: '3. Major Changes Compared to Previous Regulations',
          content: [
            'GPSR has significantly upgraded the EU\'s existing safety requirements in multiple aspects, particularly emphasizing responsibilities at the front end of the product lifecycle.',
            '• Deepened Risk Assessment: GPSR requires companies to conduct more comprehensive and detailed risk assessments, covering not only traditional risks but also proactively considering potential safety hazards arising from new technology applications.',
            '• Strengthened Technical Documentation: The regulation\'s requirements for technical documentation have become more stringent and detailed. Companies must not only prove their product compliance but also record in detail the risk assessment process and safety measures taken.',
            '• Front-loaded Safety Responsibility: GPSR emphasizes that manufacturers must deeply integrate safety functions at the product design stage, rather than merely meeting basic compliance standards.',
            '• Upgraded Traceability: Implementing a more stringent full-chain traceability system, ensuring every link from production to consumption is clearly traceable, providing support for efficient market supervision and product recalls.',
            '• Coordinated Market Supervision: Promoting more proactive cooperation between companies and market regulatory authorities, shifting from "passive response to problems" to "proactive risk prevention".'
          ]
        },
        section4: {
          title: '4. Compliance Obligations for Different Market Roles',
          content: [
            'GPSR sets clear legal responsibilities for each participant in the supply chain (manufacturers, importers, distributors).',
            '• Manufacturer Obligations:',
            'As the primary responsible party for product safety, manufacturers must ensure product safety throughout the design and production process. This includes conducting thorough risk assessments, establishing and maintaining complete technical documentation, ensuring product traceability, and affixing compliance marks such as CE when necessary. After products enter the market, any safety incidents must be immediately reported to competent authorities and effective recall procedures must be initiated.',
            '• Importer Obligations:',
            'If the manufacturer is located outside the EU, the above responsibilities transfer to importers within the EU. Importers are responsible for strictly verifying that manufacturers have fulfilled all compliance procedures before placing products on the market and ensuring complete technical documentation. Additionally, importers must maintain compliance records, conduct sampling inspections of imported products, and bear responsibility for reporting safety issues.',
            '• Distributor Obligations:',
            'Before distributing products, distributors must conduct verification to ensure products bear necessary compliance marks (such as CE marks) and safety instructions. During distribution, they are responsible for monitoring product safety. Once problems are discovered or incident reports are received, they must promptly notify manufacturers or importers and relevant authorities, and actively assist in recall efforts.'
          ]
        }
      }
    };
    return content[locale]?.[key] || content.en[key];
  };

  return (
    <div className="min-h-screen">
      <SEO
        title={seoConfig.title}
        description={seoConfig.description}
        keywords={seoConfig.keywords}
        locale={locale}
        path={`/${locale}/gpsr`}
      />
      
      {/* Header Section */}
      <section className="bg-primary text-white py-10">
        <div className="container mx-auto px-6 lg:px-12">
          <h1 className="text-4xl md:text-5xl font-black font-roboto text-center">
            {getContent('title')}
          </h1>
        </div>
      </section>

      {/* Content Section */}
      <section className="bg-gray-50 py-16 mb-20">
        <div className="container mx-auto px-6 lg:px-12">
          

          {/* Article Content */}
          <div className="max-w-6xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-8 lg:p-12">
              {/* Article Title */}
              <h1 className="text-2xl lg:text-3xl font-black text-gray-900 mb-8 text-center mb-12">
                {getContent('articleTitle')}
              </h1>

              {/* Section 1 */}
              <div className="mb-12">
                <h2 className="text-xl lg:text-2xl font-bold text-primary mb-6">
                  {getContent('section1').title}
                </h2>
                <div className="space-y-4">
                  {getContent('section1').content.map((paragraph: string, index: number) => (
                    <p key={index} className="text-gray-700 text-lg leading-relaxed">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>

              {/* Section 2 */}
              <div className="mb-12">
                <h2 className="text-xl lg:text-2xl font-bold text-primary mb-6">
                  {getContent('section2').title}
                </h2>
                <div className="space-y-4">
                  {getContent('section2').content.map((paragraph: string, index: number) => (
                    <p key={index} className="text-gray-700 text-lg leading-relaxed">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>

              {/* Section 3 */}
              <div className="mb-12">
                <h2 className="text-xl lg:text-2xl font-bold text-primary mb-6">
                  {getContent('section3').title}
                </h2>
                <div className="space-y-4">
                  {getContent('section3').content.map((paragraph: string, index: number) => (
                    <p key={index} className="text-gray-700 text-lg leading-relaxed">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>

              {/* Section 4 */}
              <div className="mb-8">
                <h2 className="text-xl lg:text-2xl font-bold text-primary mb-6">
                  {getContent('section4').title}
                </h2>
                <div className="space-y-4">
                  {getContent('section4').content.map((paragraph: string, index: number) => (
                    <p key={index} className="text-gray-700 text-lg leading-relaxed">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
