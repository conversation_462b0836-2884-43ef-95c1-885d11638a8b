import SEO from '@/components/SEO';
import { getSEOConfig } from '@/lib/seo-config';

export default async function HomePage({
  params
}: {
  params: Promise<{locale: string}>;
}) {
  const {locale} = await params;
  const seoConfig = getSEOConfig(locale, 'home');

  // Get page content based on locale
  const getContent = (key: string) => {
    const content: Record<string, Record<string, string | string[]>> = {
      en: {
        title: 'Strategic Partner for',
        titleHighlight: 'EU Market Access',
        services: [
          'Importer and Distributor Services',
          'Authorized Representative and Regulatory Compliance',
          'Clinical Trial Management',
          'Regulatory Support for'
        ],
        regulations: [
          'MDR (Medical Devices Regulation EU 2017/745)',
          'IVDR (In Vitro Diagnostics Regulation EU 2017/746)',
          'GPSR (General Product Safety Regulation EU 2019/1020)'
        ],
        whatWeDoTitle: 'How do we help you enter the EU market?',
        whatWeDoDescription: 'Riomavix is an independent company established in 2018 to support manufacturers aiming to enter the European market under the new MDR/IVDR regulations. Previously, Riomavix operated as an agency specializing in international trade across various sectors. Our work philosophy is highly practical and solution-oriented. We adapt to the specific needs of each client to provide the most effective solutions with full assurance and compliance.',
        coreValues: ['Security', 'Confidence', 'Service'],
        coreValues1:'Security',
        coreValues2:'Confidence',
        coreValues3:'Service',
        servicesTitle: 'SERVICES',
        serviceCards: [
          'EU Authorised Representative',
          'European Distribution Services',
          'Importer Services',
          'Customized Reporting Service',
          'Online Platform Services'
        ],
        serviceSubtitles: [
          'MDR/IVDR/GPSR',
          '',
          '',
          '',
          ''
        ],
        medicalDevicesTitle: 'MEDICAL DEVICES FROM CHINA',
        enterMarketTitle: 'Enter the European market',
        enterMarketDescription: 'We simplify the process for manufacturers entering the European market under the new MDR and IVDR regulations. Riomavix offers a wide range of flexible solutions, allowing you to choose the cooperation model that best fits your needs:',
        enterMarketServices: [
          'Authorized Representative Services',
          'Clinical Trial Management',
          'Logistics and Distribution Solutions'
        ],
        whyChooseTitle: 'WHY CHOOSE RIOMAVIX?',
        whyChooseDescription: 'Seamless access to the European market without fixed costs or the need to establish your own infrastructure.',
        whyChoosePoints: [
          'A 100% safe and flexible business model tailored for medical devices.',
          'We exclusively focus on medical devices, ensuring specialized service and compliance.',
          'All cooperation models are designed to give manufacturers maximum control and adaptability.'
        ],
        contactUsButton: 'Contact us',
        whyImportantTitle: 'Why is compliance with EU regulations essential?',
        whyImportantDescription: 'Our key focus is the representation and distribution of medical devices within the European Union.',
        whatChangedTitle: 'Key regulatory changes in the EU',
        whatChangedDescription: 'On May 2021, the European Commission implemented two major regulatory updates:',
        regulatoryUpdates: [
          'Medical Devices Regulation (MDR)',
          'In Vitro Diagnostic Medical Devices Regulation (IVDR)'
        ],
        regulatoryExplanation: 'These new regulations were introduced to modernize EU legislation in response to medical advancements and to provide stronger protection for public health and patient safety.',
        stakeholderRoles: 'They clearly define the roles and responsibilities of all stakeholders in the medical device supply chain.',
        complianceStatement: 'Riomavix ensures full compliance with these legal requirements and takes responsibility for all formal obligations on behalf of our clients.',
        keyPointsTitle: 'KEY POINTS OF THE NEW REGULATIONS',
        keyPoints: [
          'The MDR and IVDR introduce several significant changes aimed at improving product quality, oversight, and transparency within the European market.',
          'Enhanced focus on safety management throughout the entire product life cycle, supported by clinical data.',
          'Stricter requirements for the designation of notified bodies, ensuring greater reliability in certification processes.',
          'Increased control and oversight by national competent authorities and the European Commission.',
          'Clearer definitions of responsibilities for manufacturers, authorized representatives, importers, and distributors.',
          'Greater transparency, requiring that information about products, clinical investigations, and performance studies be publicly available to demonstrate regulatory compliance.'
        ],
        requestAssessmentButton: 'Request for Assessment',
        contactUsButton2: 'Contact us'
      },
      zh: {
        title: '欧盟市场准入',
        titleHighlight: '战略合作伙伴',
        services: [
          '进口与分销服务',
          '欧盟授权代表和法规合规',
          '临床试验管理',
          '法规支持（涵盖以下领域）'
        ],
        regulations: [
          'MDR（Medical Devices Regulation 即医疗器械法规 EU 2017/745）',
          'IVDR（In Vitro Diagnostics Regulation 即体外诊断法规 EU 2017/746）',
          'GPSR（General Product Safety Regulation 即通用产品安全法规 EU 2019/1020）'
        ],
        whatWeDoTitle: '关于我们',
        whatWeDoDescription: 'Riomavix成立于2018年，是一家独立企业，专注于协助制造商依据MDR/IVDR新法规进入欧洲市场。此前，Riomavix曾作为国际贸易代理机构活跃于多个行业。我们的运营理念以高度务实和解决方案为导向：根据客户特定需求定制服务，确保合规性并提供高效解决方案。',
        coreValues: ['安全', '信任', '服务'],
         coreValues1:'安全',
        coreValues2:'信任',
        coreValues3:'服务',
        servicesTitle: '我们的服务',
        serviceCards: [
          '欧盟授权代表',
          '欧洲分销服务',
          '进口服务',
          '定制报告服务',
          '在线平台服务'
        ],
        serviceSubtitles: [
          'MDR/IVDR/GPSR',
          '',
          '',
          '',
          ''
        ],
        medicalDevicesTitle: '中国医疗器械',
        enterMarketTitle: '进入欧洲市场',
        enterMarketDescription: '我们简化制造商依据MDR/IVDR法规进入欧洲市场的流程。Riomavix提供灵活的解决方案，客户可根据需求选择合作模式：',
        enterMarketServices: [
          '欧盟授权代表服务',
          '临床试验管理',
          '物流和分销解决方案'
        ],
        whyChooseTitle: '为何选择RIOMAVIX？',
        whyChooseDescription: '零固定成本、无需自建基础设施，即可无缝进入欧洲市场',
        whyChoosePoints: [
          '专为医疗器械设计的100%安全灵活商业模式。',
          '专注医疗器械领域，确保专业服务和全面合规。',
          '所有合作模式均旨在为制造商提供最大控制权和适应性。'
        ],
        contactUsButton: '联系我们',
        whyImportantTitle: '业务重点',
        whyImportantDescription: '我们的核心业务聚焦于欧盟境内的医疗器械代表与分销。',
        whatChangedTitle: '近期法规变化',
        whatChangedDescription: '2021年5月，欧盟委员会实施了两项重大监管更新：',
        regulatoryUpdates: [
          '医疗器械法规（MDR）',
          '体外诊断医疗器械法规（IVDR）'
        ],
        regulatoryExplanation: '这些法规旨在现代化欧盟立法，应对医疗技术进步，并加强公共卫生与患者安全保护。',
        stakeholderRoles: '法规明确了医疗器械供应链中各参与方的角色与责任。',
        complianceStatement: 'Riomavix确保全面合规，并代表客户承担所有法定义务。',
        keyPointsTitle: '新法规要点',
        keyPoints: [
          'MDR与IVDR引入多项重大变革，旨在提升产品安全、市场监管与透明度：',
          '强化全生命周期安全管理，依赖临床数据支持。',
          '更严格的公告机构指定要求，确保认证流程可靠性。',
          '加强国家主管机构与欧盟委员会的监督管控。',
          '明确制造商、授权代表、进口商与分销商的责任界定。',
          '提升透明度，要求公开产品信息、临床研究及性能评估数据以证明合规性。'
        ],
        requestAssessmentButton: '申请评估',
        contactUsButton2: '联系我们'
      },
      es: {
        title: 'Socio Estratégico para',
        titleHighlight: 'el Acceso al Mercado de la UE',
        services: [
          'Servicios de Importación y Distribución',
          'Representante Autorizado y Cumplimiento Regulatorio',
          'Gestión de Ensayos Clínicos',
          'Soporte Regulatorio para'
        ],
        regulations: [
          'MDR (Reglamento de Dispositivos Médicos de la UE 2017/745)',
          'IVDR (Reglamento sobre Diagnósticos In Vitro de la UE 2017/746 )',
          'GPSR (Reglamento General de Seguridad de los Productos de la UE 2019/1020)'
        ],
        whatWeDoTitle: '¿Cómo te ayudamos a ingresar al mercado de la UE?',
        whatWeDoDescription: 'Riomavix es una empresa independiente fundada en 2018 para apoyar a los fabricantes que buscan ingresar al mercado europeo bajo las nuevas regulaciones MDR/IVDR. Anteriormente, Riomavix operaba como una agencia especializada en comercio internacional en diversos sectores. Nuestra filosofía de trabajo es altamente práctica y orientada a soluciones: nos adaptamos a las necesidades específicas de cada cliente para ofrecer las soluciones más efectivas, con total garantía y cumplimiento normativo.',
        coreValues: ['Seguridad', 'Confianza', 'Servicio'],
          coreValues1:'Seguridad',
        coreValues2:'Confianza',
        coreValues3:'Servicio',
        servicesTitle: 'SERVICIOS',
        serviceCards: [
          'Representante Autorizado UE',
          'Servicios de Distribución Europea',
          'Servicios de Importación',
          'Servicio de Informes Personalizados',
          'Servicios de Plataforma Online'
        ],
        serviceSubtitles: [
          'MDR/IVDR/GPSR',
          '',
          '',
          '',
          ''
        ],
        medicalDevicesTitle: 'DISPOSITIVOS MÉDICOS DE CHINA',
        enterMarketTitle: 'Entrar al mercado europeo',
        enterMarketDescription: 'Simplificamos el proceso para los fabricantes que desean ingresar al mercado europeo bajo las nuevas regulaciones MDR e IVDR. Riomavix ofrece una amplia gama de soluciones flexibles, permitiéndole elegir el modelo de cooperación que mejor se adapte a tus necesidades:',
        enterMarketServices: [
          'Servicios de Representante Autorizado',
          'Gestión de Ensayos Clínicos',
          'Soluciones de Logística y Distribución'
        ],
        whyChooseTitle: '¿POR QUÉ ELEGIR RIOMAVIX?',
        whyChooseDescription: 'Acceso fluido al mercado europeo sin costos fijos ni la necesidad de establecer tu propia infraestructura',
        whyChoosePoints: [
          'Un modelo de negocio 100 % seguro y flexible diseñado especialmente para dispositivos médicos',
          'Nos enfocamos exclusivamente en dispositivos médicos, garantizando un servicio especializado y un cumplimiento normativo exhaustivo',
          'Todos los modelos de cooperación están diseñados para ofrecer a los fabricantes el máximo control y adaptabilidad'
        ],
        contactUsButton: 'Contáctanos',
        whyImportantTitle: '¿Por qué es clave cumplir con las normativas de la UE?',
        whyImportantDescription: 'Hoy, nuestro enfoque principal es la representación y distribución de dispositivos médicos dentro de la Unión Europea.',
        whatChangedTitle: 'Principales cambios regulatorios en la UE',
        whatChangedDescription: 'En mayo de 2021, la Comisión Europea implementó dos importantes actualizaciones regulatorias:',
        regulatoryUpdates: [
          'Reglamento de Dispositivos Médicos (MDR)',
          'Reglamento de Dispositivos Médicos de Diagnóstico In Vitro (IVDR)'
        ],
        regulatoryExplanation: 'Estas nuevas normativas se introdujeron para modernizar la legislación de la UE en respuesta a los avances médicos y para brindar una mayor protección a la salud pública y la seguridad de los pacientes.',
        stakeholderRoles: 'Definen claramente los roles y responsabilidades de todas las partes involucradas en la cadena de suministro de dispositivos médicos.',
        complianceStatement: 'Riomavix garantiza el cumplimiento total de estos requisitos legales y asume la responsabilidad de todas las obligaciones formales en nombre de nuestros clientes.',
        keyPointsTitle: 'PUNTOS CLAVE DE LAS NUEVAS REGULACIONES',
        keyPoints: [
          'El MDR y el IVDR introducen varios cambios significativos orientados a mejorar la seguridad del producto, la supervisión y la transparencia dentro del mercado europeo.',
          'Mayor énfasis en la gestión de la seguridad a lo largo de todo el ciclo de vida del producto, respaldado por datos clínicos.',
          'Requisitos más estrictos para la designación de organismos notificados, garantizando una mayor fiabilidad en los procesos de certificación.',
          'Mayor control y supervisión por parte de las autoridades competentes nacionales y la Comisión Europea.',
          'Definiciones más claras de las responsabilidades de fabricantes, representantes autorizados, importadores y distribuidores.',
          'Mayor transparencia, exigiendo que la información sobre productos, investigaciones clínicas y estudios de desempeño esté disponible públicamente para demostrar el cumplimiento normativo.'
        ],
        requestAssessmentButton: 'Solicitar Evaluación',
        contactUsButton2: 'Contáctanos'
      }
    };
    return content[locale]?.[key] || content.en[key];
  };

  return (
    <div className="relative">
      <SEO
        title={seoConfig.title}
        description={seoConfig.description}
        keywords={seoConfig.keywords}
        locale={locale}
        path={`/${locale}`}
      />
      {/* Hero Banner Section */}
      <section
        className="relative bg-cover bg-center bg-no-repeat flex items-center"
        style={{
          backgroundImage: 'url(/images/hero/banner.png)',
          height: '630px'
        }}
      >
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-slate-900/20"></div>

        {/* Content Container */}
        <div className="relative z-10 container mx-auto px-6 lg:px-12">
          <div className="max-w-2xl">
            {/* Main Title */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-black text-white mb-4 leading-tight">
              {getContent('title')}
              <br />
              <span className="text-cyan-400">
                {getContent('titleHighlight')}
              </span>
            </h1>

            {/* Services Box */}
            <div className="bg-cyan-500/82 backdrop-blur-sm rounded-lg p-6 mt-8 shadow-xl">
              <ul className="space-y-1">
                {(getContent('services') as string[]).map((service, index) => (
                  <li key={index} className="flex items-start text-white">
                    <span className="inline-block w-1.5 h-1.5 bg-white rounded-full mt-3 mr-3 flex-shrink-0"></span>
                    <span className="text-lg font-black font-roboto">
                      {service}
                    </span>
                  </li>
                ))}
              </ul>

              {/* Regulations List */}
              <div className="mt-2 ml-7">
                   <ul className="space-y-1">
                {(getContent('regulations') as string[]).map((regulation, index) => (
               
                     <li key={index} className="text-white/90 text-sm md:text-base mb-1 font-roboto font-bold">
                        <span className="inline-block w-1.5 h-1.5 bg-white rounded-full mt-3 mr-3 flex-shrink-0"></span>
                        {index===2?<a className="text-base font-medium font-roboto underline text-fuchsia-100" href={`/${locale}/gpsr/`}>
                          {regulation}
                        </a>:<span className="text-base font-medium font-roboto">
                          {regulation}
                        </span>
                        }
                      </li>
                ))}
                   </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What We Do Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-6 lg:px-12">
          {/* Section Title */}
          <h2 className="text-3xl md:text-4xl font-bold text-primary text-center mb-12">
            {getContent('whatWeDoTitle')}
          </h2>

          {/* Description */}
          <div className="max-w-6xl mx-auto text-justify mb-16">
            <p className="text-gray-700 text-lg leading-relaxed">
              {getContent('whatWeDoDescription')}
            </p>
          </div>

          {/* Core Values */}
          <div className="flex justify-center">
            <div className="bg-primary rounded-full px-16 py-5 flex items-center space-x-16">
        
                <div className="text-center">
                  <div className="w-16 h-16 flex items-center justify-center mb-1 mx-auto">
                   
                       <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1" stroke="currentColor" className="stroke-white size-13">
                       <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z" />
                    </svg>
                    {/* </div> */}
                  </div>
                  <span className="text-white font-semibold text-lg">
                    {getContent('coreValues1')}
                  </span>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 flex items-center justify-center mb-1 mx-auto">
                   
                    {/* <div className="w-8 h-8"> */}
                     
                    <svg  xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 32 32" strokeWidth="1" className="stroke-white size-12">
<path d="M12.259,23.36c-2.614,0-4.759,2.043-4.885,4.651C7.37,28.1,7.332,28.185,7.269,28.248
	l-3.015,3.006l-0.509-0.51l2.918-2.909c0.213-2.925,2.644-5.196,5.595-5.196h7.75c0.75,0,1.36,0.61,1.36,1.36
	c0,0.066-0.003,0.132-0.009,0.196l4.999-4.062c0.663-0.666,1.79-0.667,2.48,0.024c0.692,0.692,0.691,1.818,0.001,2.51l-6.102,6.102
	c-0.369,0.369-0.857,0.607-1.375,0.672l-7.197,0.899l-0.917,0.913l-0.508-0.51l1.004-1c0.057-0.057,0.13-0.093,0.209-0.103
	l7.32-0.915c0.359-0.044,0.698-0.21,0.954-0.467l6.102-6.102c0.411-0.41,0.411-1.079,0-1.491c-0.411-0.411-1.081-0.41-1.49,0.001
	l-6.237,5.07c-0.42,0.386-0.979,0.621-1.593,0.621h-5.001V25.64h5.001c0.393,0,0.753-0.139,1.036-0.369l-0.003-0.004l0.092-0.075
	c0.315-0.299,0.513-0.722,0.513-1.189c0-0.354-0.287-0.642-0.64-0.642L12.259,23.36L12.259,23.36z M17,18.36
	c-0.092,0-0.184-0.035-0.254-0.105L8.82,10.328C7.858,9.367,7.329,8.092,7.329,6.737c0-1.365,0.529-2.646,1.491-3.606
	c0.96-0.961,2.238-1.49,3.596-1.491c0,0,0.001,0,0.002,0c1.359,0,2.637,0.529,3.599,1.491L17,4.114l0.983-0.983
	c0.962-0.961,2.24-1.491,3.6-1.491c0.001,0,0.003,0,0.004,0c1.357,0.001,2.634,0.53,3.595,1.491c0.961,0.961,1.49,2.239,1.49,3.599
	c0,1.359-0.529,2.637-1.49,3.599l-7.927,7.927C17.185,18.325,17.092,18.36,17,18.36z M12.417,2.36c0,0-0.001,0-0.002,0
	c-1.166,0-2.262,0.455-3.087,1.28s-1.28,1.925-1.28,3.098c0,1.162,0.455,2.256,1.28,3.082L17,17.491l7.672-7.672
	c0.825-0.826,1.279-1.923,1.279-3.09s-0.454-2.264-1.279-3.09c-0.824-0.824-1.92-1.279-3.085-1.28c-0.001,0-0.003,0-0.004,0
	c-1.168,0-2.265,0.455-3.09,1.28l-1.238,1.238c-0.142,0.141-0.369,0.14-0.509,0L15.507,3.64C14.682,2.814,13.584,2.36,12.417,2.36z"
	></path>
</svg>
                    {/* </div> */}
                  </div>
                  <span className="text-white font-semibold text-lg">
                    {getContent('coreValues2')}
                  </span>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 flex items-center justify-center mb-1 mx-auto">
                   
                  
                    <svg version="1.1"  xmlns="http://www.w3.org/2000/svg" strokeWidth="1"  viewBox="0 0 32 32" className="stroke-white size-12" >
<path   d="M31.36,31h-0.72c0-6.432-4.777-12.232-11.359-13.792c-0.15-0.036-0.261-0.163-0.275-0.317
	c-0.015-0.153,0.071-0.299,0.212-0.362c1.328-0.591,2.47-1.537,3.302-2.734l0.592,0.411c-0.725,1.043-1.666,1.911-2.753,2.546
	C26.785,18.688,31.36,24.54,31.36,31z M1.36,31H0.64c0-6.46,4.574-12.312,11.002-14.248c-2.634-1.539-4.291-4.375-4.291-7.465
	c0-4.768,3.879-8.647,8.648-8.647c2.583,0,5.01,1.141,6.659,3.13l-0.555,0.46C20.593,2.406,18.368,1.36,16,1.36
	c-4.372,0-7.928,3.556-7.928,7.927c0,3.125,1.849,5.968,4.711,7.241c0.141,0.063,0.226,0.209,0.212,0.362
	c-0.014,0.154-0.125,0.281-0.275,0.317C6.137,18.768,1.36,24.568,1.36,31z M19,13.36c-0.75,0-1.36-0.61-1.36-1.36
	s0.61-1.36,1.36-1.36c0.621,0,1.146,0.418,1.308,0.987C22.172,11.47,23.64,9.903,23.64,8V6h0.721v2c0,2.298-1.788,4.187-4.046,4.349
	C20.16,12.93,19.63,13.36,19,13.36z M19,11.36c-0.353,0-0.64,0.287-0.64,0.64s0.287,0.64,0.64,0.64s0.64-0.287,0.64-0.64
	S19.353,11.36,19,11.36z"></path>
</svg>                
                  </div>
                  <span className="text-white font-semibold text-lg">
                    {getContent('coreValues3')}
                  </span>
                </div>
              {/* ))} */}
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-100">
        <div className="container mx-auto px-6 lg:px-12">
          {/* Section Title */}
          <h2 className="text-3xl md:text-4xl font-black text-black text-center mb-16">
            {getContent('servicesTitle')}
          </h2>

          {/* Service Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {(getContent('serviceCards') as string[]).map((service, index) => {
              const subtitle = (getContent('serviceSubtitles') as string[])[index];
              // Map service index to service ID for linking
              const serviceIds = ['authorised', 'distribution', 'importer', 'reporting', 'medixbuy'];
              const serviceId = serviceIds[index];

              return (
                <a
                  key={index}
                  href={`/${locale}/services/?service=${serviceId}`}
                  className="group rounded-lg p-6 text-center transition-all duration-300 bg-white text-gray-800  hover:bg-cyan-500 hover:text-white hover:shadow-xl/30 block"
                >
                  {/* Service Image Placeholder */}
                  <div className="w-auto max-w-32 h-32 rounded-lg my-4 border-gray-300 border-1 text-center mx-auto group-hover:bg-white/20 transition-colors duration-300" 
                     style={{
              backgroundImage: `url(/images/gallery/service${index + 1}.jpg)`,
              backgroundSize:'cover',
        }}
                  >
                    {/* <div className="w-16 h-16 rounded-lg bg-gray-300 group-hover:bg-white/30 transition-colors duration-300"></div> */}
                  </div>

                  {/* Service Title */}
                  <h3 className="font-black text-lg mb-2 text-gray-800 group-hover:text-white transition-colors duration-300">
                    {service}
                  </h3>

                  {/* Service Subtitle */}
                  {subtitle && (
                    <p className="text-sm text-gray-600 group-hover:text-white/80 transition-colors duration-300">
                      {subtitle}
                    </p>
                  )}
                </a>
              );
            })}
          </div>
        </div>
      </section>

      {/* Medical Devices from China Section */}
      <section className="py-8 mx-auto bg-gray-100">
        <div className="container mx-auto px-6 lg:px-12">
          <div className="mb-8 max-w-6xl">
              <h3 className={`text-cyan-500 font-bold  tracking-widest uppercase mb-2 ${locale==='zh'?'text-lg':''}`}>
                {getContent('medicalDevicesTitle')}
              </h3>
              <h2 className="text-2xl lg:text-3xl font-black  text-primary mb-5 leading-tight">
                {getContent('enterMarketTitle')}
              </h2>
              <div  className={`${locale==='zh'?'text-lg':''}`}>
              <p className="text-gray-900 font-semibold leading-relaxed mb-5">
                {getContent('enterMarketDescription')}
              </p>
               {/* Services List */}
              <ul className="space-y-1">
                {(getContent('enterMarketServices') as string[]).map((service, index) => (
                  <li key={index} className="flex items-start">
                    <span className="inline-block w-1.5 h-1.5 bg-gray-800 rounded-full mt-2.5 mr-4 flex-shrink-0"></span>
                    <span className="text-gray-800">{service}</span>
                  </li>
                ))}
              </ul>
              </div>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
            {/* Left Content */}
            <div className="lg:pr-8 rounded-2xl"
            style={{
              backgroundImage: 'url(/images/gallery/docter2.png)',
              backgroundSize:'cover',
              height: '500px',
        }}
            >
             {/* Doctor Image */}
              {/* <div className="bg-gradient-to-br from-blue-100 to-cyan-100 rounded-2xl h-96 mb-8 flex items-center justify-center overflow-hidden">
                <div className="text-gray-400 text-center">
                  <div className="w-32 h-32 bg-white/50 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <svg className="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                  </div>
                  <p className="text-sm font-medium">Medical Professional</p>
                </div>
              </div> */}

             
            </div>

            {/* Right Content - Image and Why Choose */}
            <div className="relative">
             

              {/* Why Choose Riomavix */}
               <div  className={`${locale==='zh'?'text-lg':''}`}>
               <h3 className={`text-cyan-500 font-bold  tracking-widest uppercase mb-2 ${locale==='zh'?'text-lg':''}`}>
                  {getContent('whyChooseTitle')}
                </h3>
                <p className="text-gray-900 font-semibold text-lg mb-6 leading-relaxed">
                  {getContent('whyChooseDescription')}
                </p>

                <ul className="space-y-1 mb-8">
                  {(getContent('whyChoosePoints') as string[]).map((point, index) => (
                    <li key={index} className="flex items-start">
                      <span className="inline-block w-1.5 h-1.5 bg-gray-800 rounded-full mt-2.5 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-700 leading-relaxed">{point}</span>
                    </li>
                  ))}
                </ul>

                {/* Contact Us Button */}
                <a
                  href={`/${locale}/contact/`}
                  className="inline-flex items-center justify-center bg-white border-2 border-cyan-500 text-cyan-500 px-8 py-3 rounded-full font-semibold text-sm hover:bg-cyan-500 hover:text-white transition-all duration-300 shadow-sm hover:shadow-md"
                >
                  {getContent('contactUsButton')}
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Is This Important Section */}
      <section className="py-10 bg-primary text-white">
        <div className="container mx-auto lg:px-12">
          <div className="text-center mb-6">
            <h2 className="text-2xl lg:text-3xl font-black mb-2 leading-tight">
              {getContent('whyImportantTitle')}
            </h2>
            <p className="text-lg text-white/90 max-w-4xl mx-auto leading-relaxed text-left">
              {getContent('whyImportantDescription')}
            </p>
          </div>

          {/* What Has Changed Recently */}
          <div className="max-w-8xl mx-auto">
            <div className="text-center mb-6">
              <h3 className="text-2xl lg:text-3xl font-black  text-cyan-300 mb-4 leading-tight inline-block border-b-3 border-orange-500">
                {getContent('whatChangedTitle')}
              </h3>
              <div className="text-left text-lg  text-white/90 leading-relaxed">
              <div className="max-w-4xl mx-auto ">

             
              <p className="mb-2 ">
              {getContent('whatChangedDescription')}
              </p>
               <ul className="space-y-1 mb-2">
                  {(getContent('regulatoryUpdates') as string[]).map((point, index) => (
                    <li key={index} className="flex items-start">
                      <span className="inline-block w-1 h-1 bg-white rounded-full mt-2.5 mr-4 flex-shrink-0"></span>
                      <span className="text-base leading-relaxed">{point}</span>
                    </li>
                  ))}
                </ul>
              <p className="mb-2">{getContent('regulatoryExplanation')}</p>
              <p className="mb-2">{getContent('stakeholderRoles')}</p>
              <p className="mb-2">{getContent('complianceStatement')}</p>
              

 </div>
              </div>
             

            </div>

            <div className="max-w-4xl mx-auto text-center space-y-6 text-white/90 text-base leading-relaxed">
            
            </div>
          </div>
        </div>
      </section>

      {/* Key Points of the New Regulations Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-6 lg:px-12">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 items-top">
            {/* Left Content - Key Points */}
            <div className="lg:pr-8 col-span-3">
              <h2 className={`${locale==='zh'?'text-xl':'text-lg'} text-cyan-500 font-bold tracking-wide mb-8`}>
                {getContent('keyPointsTitle')}
              </h2>

              {/* Key Points List */}
              <ul className={`space-y-1 ${locale==='zh'?'text-lg':''}`}>
                {(getContent('keyPoints') as string[]).map((point, index) => (
                  <li key={index} className="flex items-start">
                    <span className="inline-block w-2 h-2 bg-primary rounded-full mt-2.5 mr-4 flex-shrink-0"></span>
                    <span className="text-gray-700  leading-relaxed">{point}</span>
                  </li>
                ))}
              </ul>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 mt-10">
                {/* Contact Us Button */}
                <a
                  href={`/${locale}/contact/`}
                  className="mb-10 inline-flex items-center justify-center bg-white border-2 border-cyan-500 text-cyan-500 px-8 py-3 rounded-full font-semibold text-sm hover:bg-cyan-500 hover:text-white transition-all duration-300 shadow-sm hover:shadow-md"
                >
                  {getContent('contactUsButton')}
                </a>
                {/* <a
                  href={`/${locale}/contact/`}
                  className="inline-flex items-center justify-center bg-white border-2 border-primary text-cyan-500 px-8 py-3 rounded-full font-semibold text-sm hover:bg-cyan-50 transition-all duration-300"
                  // style={{
                  //       position: "absolute",
                  //    paddingRight: "159px",
                  //    width:"370px"
                  // }}
                >
                  {getContent('contactUsButton2')}
                </a> */}
                {/* <a
                  href={`/${locale}/contact/`}
                  className="inline-flex items-center justify-center bg-primary text-white px-8 py-3 rounded-full font-semibold text-sm hover:bg-cyan-600 transition-all duration-300"
                  // style={{
                  //       marginLeft: "220px",
                  //        zIndex:"999",
                  //       marginTop:"2px",
                  //       width:"150px"
                  // }}
                >
                  {getContent('contactUsButton2')}
                </a> */}
              </div>
            </div>

            {/* Right Content - EU Flag Image */}
            <div className="relative col-span-2"
            >
              <div className="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl overflow-hidden" 
                 style={{
              backgroundImage: 'url(/images/gallery/service1.jpg)',
              backgroundSize:'cover',
              height: '500px',
        }}
              >
                
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
