@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

html[lang="zh"] {
  --font-size-xs: var(--font-size-sm);   /* xs -> sm */
  --font-size-sm: var(--font-size-base);  /* sm -> base */
  --font-size-base: var(--font-size-lg);   /* base -> lg */
  --font-size-lg: var(--font-size-xl);   /* lg -> xl */
  --font-size-xl: var(--font-size-2xl);  /* xl -> 2xl */
  --font-size-2xl: var(--font-size-3xl);  /* 2xl -> 3xl */
  --font-size-3xl: 2.25rem; /* 36px, 为 3xl 提供一个更大的值 */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-roboto: var(--font-roboto);
  --color-primary: #286aa4;
  --color-secondary: #31c3e0;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  /* color: var(--foreground); */
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Microsoft YaHei UI', 'SimSun', 'SimHei',Arial, Helvetica, sans-serif;
  /* line-height: 1rem; */
}

/* Apply Roboto font to all headings */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-roboto), system-ui, sans-serif;
}

html[lang="zh"] h1,html[lang="zh"] h2, html[lang="zh"] h3, html[lang="zh"] h4, html[lang="zh"] h5, html[lang="zh"] h6 {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Microsoft YaHei UI', 'SimSun', 'SimHei', system-ui, sans-serif;
}

ul li{
  /* margin:0; */
}
