import { MetadataRoute } from 'next';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://riomavix.com'; // 替换为实际域名
  const locales = ['en', 'zh', 'es'];
  const pages = ['', '/about', '/services', '/contact', '/gpsr'];
  
  const sitemap: MetadataRoute.Sitemap = [];
  
  // 为每个语言和页面生成sitemap条目
  locales.forEach(locale => {
    pages.forEach(page => {
      sitemap.push({
        url: `${baseUrl}/${locale}${page}`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: page === '' ? 1.0 : 0.8, // 首页优先级最高
      });
    });
  });
  
  // 添加默认语言重定向
  pages.forEach(page => {
    sitemap.push({
      url: `${baseUrl}${page}`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: page === '' ? 1.0 : 0.8,
    });
  });
  
  return sitemap;
}
