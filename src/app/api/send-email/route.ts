import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  try {
    const { name, email, company, requirements, locale } = await request.json();

    // 验证必填字段
    if (!name || !email || !company || !requirements) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    // 验证字段长度
    if (name.length > 50 || email.length > 50 || company.length > 50) {
      return NextResponse.json(
        { error: 'Name, email, and company must be 50 characters or less' },
        { status: 400 }
      );
    }

    if (requirements.length > 500) {
      return NextResponse.json(
        { error: 'Requirements must be 500 characters or less' },
        { status: 400 }
      );
    }

    // 创建邮件传输器
    const transporter = nodemailer.createTransport({
      host: 'smtp.qiye.aliyun.com',
      port: 465,
      secure: true,
      auth: {
        user: '<EMAIL>',
        pass: 'AbC?123456',
      },
    });

    // 根据语言设置邮件内容
    const getEmailContent = (locale: string) => {
      const content = {
        en: {
          subject: 'New Contact Form Submission - Riomavix',
          greeting: 'New contact form submission received:',
          nameLabel: 'Name:',
          emailLabel: 'Email:',
          companyLabel: 'Company:',
          requirementsLabel: 'Requirements:',
          footer: 'This email was sent from the Riomavix contact form.'
        },
        zh: {
          subject: '新的联系表单提交 - Riomavix',
          greeting: '收到新的联系表单提交：',
          nameLabel: '姓名：',
          emailLabel: '邮箱：',
          companyLabel: '公司：',
          requirementsLabel: '需求：',
          footer: '此邮件来自Riomavix联系表单。'
        },
        es: {
          subject: 'Nueva Solicitud de Contacto - Riomavix',
          greeting: 'Nueva solicitud de contacto recibida:',
          nameLabel: 'Nombre:',
          emailLabel: 'Correo:',
          companyLabel: 'Empresa:',
          requirementsLabel: 'Requisitos:',
          footer: 'Este correo fue enviado desde el formulario de contacto de Riomavix.'
        }
      };
      return content[locale as keyof typeof content] || content.en;
    };

    const emailContent = getEmailContent(locale);

    // 邮件选项
    const mailOptions = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      subject: emailContent.subject,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #0891b2; border-bottom: 2px solid #0891b2; padding-bottom: 10px;">
            ${emailContent.greeting}
          </h2>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <p style="margin: 10px 0;"><strong>${emailContent.nameLabel}</strong> ${name}</p>
            <p style="margin: 10px 0;"><strong>${emailContent.emailLabel}</strong> ${email}</p>
            <p style="margin: 10px 0;"><strong>${emailContent.companyLabel}</strong> ${company}</p>
          </div>
          
          <div style="margin: 20px 0;">
            <h3 style="color: #374151; margin-bottom: 10px;">${emailContent.requirementsLabel}</h3>
            <div style="background-color: #ffffff; padding: 15px; border: 1px solid #d1d5db; border-radius: 6px; white-space: pre-wrap;">
${requirements}
            </div>
          </div>
          
          <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
          
          <p style="color: #6b7280; font-size: 14px; text-align: center;">
            ${emailContent.footer}
          </p>
          
          <div style="text-align: center; margin-top: 20px;">
            <img src="https://riomavix.com/images/logos/logo.png" alt="Riomavix Logo" style="height: 40px;">
          </div>
        </div>
      `,
    };

    // 发送邮件
    console.log(mailOptions);
    await transporter.sendMail(mailOptions);

    return NextResponse.json(
      { message: 'Email sent successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    );
  }
}
