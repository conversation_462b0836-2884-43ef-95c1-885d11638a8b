'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';

export default function Footer() {
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1] || 'en';

  // Get footer text based on current locale
  const getFooterText = (key: string) => {
    const texts: Record<string, Record<string, string>> = {
      en: {
        navigation: 'NAVIGATION',
        home: 'Home',
        about: 'About us',
        services: 'Our Services',
        medixbuy: 'Medixbuy',
        contact: 'Contact us',
        offices: 'OUR OFFICES',
        shanghaiOffice: 'Shanghai Office (China)',
        shanghaiAddress: 'Calle LongCao Lane 1, Edif 7, 401, Shanghai, PRC',
        europeTeam: 'Europe Team (Headquarters in Madrid, Spain)',
        madridAddress1: 'Address: Calle de Goya, 21, 1D, Madrid, España',
        madridAddress2: 'Address: Calle Almansa 55, 1D, Madrid España',
        copyright: '©2025 - Riomavix - All rights reserved.',
        linkedin: 'LinkedIn'
      },
      zh: {
        navigation: '导航',
        home: '首页',
        about: '关于我们',
        services: '我们的服务',
        medixbuy: 'Medixbuy',
        contact: '联系我们',
        offices: '我们的办公室',
        shanghaiOffice: '上海办公室（中国）',
        shanghaiAddress: '地址：上海市徐汇区龙漕路1弄7号401A室',
        europeTeam: '欧洲团队（总部位于西班牙马德里）',
        madridAddress1: '地址：Calle de Goya, 21, 1D, Madrid, España',
        madridAddress2: '地址：Calle Almansa 55, 1D, Madrid España',
        copyright: '©2025 - Riomavix - 版权所有',
        linkedin: 'LinkedIn'
      },
      es: {
        navigation: 'NAVEGACIÓN',
        home: 'Inicio',
        about: 'Sobre nosotros',
        services: 'Nuestros Servicios',
        medixbuy: 'Medixbuy',
        contact: 'Contáctanos',
        offices: 'NUESTRAS OFICINAS',
        shanghaiOffice: 'Oficina de Shanghai (China)',
        shanghaiAddress: 'Calle LongCao Lane 1, Edif 7, 401, Shanghai, RPC',
        europeTeam: 'Equipo Europeo (Sede en Madrid, España)',
        madridAddress1: 'Calle de Goya, 21, 1D, Madrid, España',
        madridAddress2: 'Calle Almansa 55, 1D, Madrid España',
        copyright: '©2025 - Riomavix - Todos los derechos reservados.',
        linkedin: 'LinkedIn'
      }
    };
    return texts[currentLocale]?.[key] || texts.en[key];
  };

  return (
    <footer className="bg-gray-100 text-gray-700 border-t border-gray-200">
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {/* Logo Section */}
          <div className="flex justify-center md:justify-start">
            <Link href={`/${currentLocale}`}>
              <Image
                src="/images/logos/logo.png"
                alt="RIOMA Logo"
                width={150}
                height={50}
                className="h-25 w-auto"
              />
            </Link>
          </div>

          {/* Navigation Section */}
          <div>
            <h3 className="text-lg font-semibold font-roboto mb-6 text-primary">
              {getFooterText('navigation')}
            </h3>
            <ul className="space-y-3">
              <li>
                <Link
                  href={`/${currentLocale}`}
                  className="text-gray-600 hover:text-cyan-500 transition-colors"
                >
                  {getFooterText('home')}
                </Link>
              </li>
              <li>
                <Link
                  href={`/${currentLocale}/about`}
                  className="text-gray-600 hover:text-cyan-500 transition-colors"
                >
                  {getFooterText('about')}
                </Link>
              </li>
              <li>
                <Link
                  href={`/${currentLocale}/services`}
                  className="text-gray-600 hover:text-cyan-500 transition-colors"
                >
                  {getFooterText('services')}
                </Link>
              </li>
              <li>
                <Link
                  href={`/${currentLocale}/medixbuy`}
                  className="text-gray-600 hover:text-cyan-500 transition-colors"
                >
                  {getFooterText('medixbuy')}
                </Link>
              </li>
              <li>
                <Link
                  href={`/${currentLocale}/contact`}
                  className="text-gray-600 hover:text-cyan-500 transition-colors"
                >
                  {getFooterText('contact')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Offices Section */}
          <div>
            <h3 className="text-lg font-semibold font-roboto mb-6 text-primary">
              {getFooterText('offices')}
            </h3>
            <div className="space-y-6">
              {/* Shanghai Office */}
              <div>
                <h4 className="font-black text-gray-800  mb-2">
                  {getFooterText('shanghaiOffice')}
                </h4>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {getFooterText('shanghaiAddress')}
                </p>
              </div>

              {/* Europe Team */}
              <div>
                <h4 className="font-black text-gray-800 mb-2">
                  {getFooterText('europeTeam')}
                </h4>
                <div className="text-gray-600 text-sm leading-relaxed space-y-1">
                  <p>{getFooterText('madridAddress1')}</p>
                  <p>{getFooterText('madridAddress2')}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-200 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-600 text-sm mb-4 md:mb-0">
            {getFooterText('copyright')}
          </p>

          {/* LinkedIn Link */}
          <div className="flex items-center">
            <a
              href="https://www.linkedin.com/company/riomavix-sl/?viewAsMember=true"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center text-primary hover:text-cyan-500 transition-colors"
            >
              <svg className="w-8 h-8 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
              {/* {getFooterText('linkedin')} */}
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}
