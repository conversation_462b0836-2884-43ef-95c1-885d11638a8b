'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Image from 'next/image';
import { useState } from 'react';

export default function Header() {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Extract current locale from pathname
  const currentLocale = pathname.split('/')[1] || 'en';

  // Get navigation text based on current locale
  const getNavText = (key: string) => {
    const texts: Record<string, Record<string, string>> = {
      en: {
        home: 'HOME',
        about: 'ABOUT US',
        services: 'OUR SERVICES',
        medixbuy: 'MEDIXBUY',
        contact: 'CONTACT US',
        language: 'ENGLISH'
      },
      zh: {
        home: '首页',
        about: '关于我们',
        services: '我们的服务',
        medixbuy: 'MEDIXBUY',
        contact: '联系我们',
        language: '中文'
      },
      es: {
        home: 'INICIO',
        about: 'SOBRE NOSOTROS',
        services: 'NUESTROS SERVICIOS',
        medixbuy: 'MEDIXBUY',
        contact: 'CONTÁCTANOS',
        language: 'ESPAÑOL'
      }
    };
    return texts[currentLocale]?.[key] || texts.en[key];
  };

  // Language switcher
  const switchLanguage = (newLocale: string) => {
    const segments = pathname.split('/');
    segments[1] = newLocale;
    return segments.join('/');
  };

  return (
    <header className="fixed top-0 left-0 right-0 bg-white shadow-sm border-b border-gray-100 z-50 w-full">
      <nav className="container mx-auto px-6 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href={`/${currentLocale}`} className="flex items-center">
              <Image
                src="/images/logos/logo.png"
                alt="RIOMA Logo"
                width={120}
                height={40}
                className="h-15 w-auto"
                priority
              />
            </Link>
          </div>

          {/* Navigation Links - Center */}
          <div className="hidden lg:flex items-center space-x-8 flex-1 justify-center">
            <Link
              href={`/${currentLocale}`}
              className={`text-sm font-black tracking-wide transition-colors duration-200 ${
                pathname === `/${currentLocale}` || pathname === `/${currentLocale}/`
                  ? 'text-cyan-500 border-cyan-500'
                  : 'text-gray-700 hover:text-cyan-500'
              }`}
            >
              {getNavText('home')}
            </Link>
            <Link
              href={`/${currentLocale}/about`}
              className={`text-sm font-black tracking-wide transition-colors duration-200 ${
                pathname.includes('/about')
                  ? 'text-cyan-500'
                  : 'text-gray-700 hover:text-cyan-500'
              }`}
            >
              {getNavText('about')}
            </Link>
            <Link
              href={`/${currentLocale}/services`}
              className={`text-sm font-black tracking-wide transition-colors duration-200 ${
                pathname.includes('/services')
                  ? 'text-cyan-500 '
                  : 'text-gray-700 hover:text-cyan-500'
              }`}
            >
              {getNavText('services')}
            </Link>
            <Link
              href={`https://medixbuy.com/`} 
              target="_blank"
              className={`text-sm font-black tracking-wide transition-colors duration-200 hover:text-cyan-500`}
            >
              {getNavText('medixbuy')}
            </Link>
            <Link
              href={`/${currentLocale}/contact`}
              className={`text-sm font-black tracking-wide transition-colors duration-200 ${
                pathname.includes('/contact')
                  ? 'text-cyan-500 '
                  : 'text-gray-700 hover:text-cyan-500'
              }`}
            >
              {getNavText('contact')}
            </Link>
          </div>

          {/* Right Side - Language & Search */}
          <div className="flex items-center space-x-4">
            {/* Language Switcher */}
            <div className="relative">
              <select
                value={currentLocale}
                onChange={(e) => {
                  const newLocale = e.target.value;
                  window.location.href = switchLanguage(newLocale);
                }}
                className="appearance-none bg-white border border-gray-300 rounded-full px-4 py-2 pr-8 text-sm font-medium text-gray-700 hover:border-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent cursor-pointer"
              >
                <option value="en">🇺🇸 ENGLISH</option>
                <option value="zh">🇨🇳 中文</option>
                <option value="es">🇪🇸 ESPAÑOL</option>
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                  <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                </svg>
              </div>
            </div>

            {/* Search Icon */}
            {/* <button className="p-2 rounded-full bg-cyan-500 text-white hover:bg-cyan-600 transition-colors duration-200">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button> */}
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2 rounded-md text-gray-700 hover:text-cyan-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-cyan-500"
              aria-label="Toggle mobile menu"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200 shadow-lg">
              <Link
                href={`/${currentLocale}`}
                onClick={() => setIsMobileMenuOpen(false)}
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                  pathname === `/${currentLocale}` || pathname === `/${currentLocale}/`
                    ? 'text-cyan-500 bg-cyan-50'
                    : 'text-gray-700 hover:text-cyan-500 hover:bg-gray-50'
                }`}
              >
                {getNavText('home')}
              </Link>
              <Link
                href={`/${currentLocale}/about`}
                onClick={() => setIsMobileMenuOpen(false)}
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                  pathname.includes('/about')
                    ? 'text-cyan-500 bg-cyan-50'
                    : 'text-gray-700 hover:text-cyan-500 hover:bg-gray-50'
                }`}
              >
                {getNavText('about')}
              </Link>
              <Link
                href={`/${currentLocale}/services`}
                onClick={() => setIsMobileMenuOpen(false)}
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                  pathname.includes('/services')
                    ? 'text-cyan-500 bg-cyan-50'
                    : 'text-gray-700 hover:text-cyan-500 hover:bg-gray-50'
                }`}
              >
                {getNavText('services')}
              </Link>
              <Link
                href={`https://medixbuy.com/`}
                target="_blank"
                onClick={() => setIsMobileMenuOpen(false)}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-cyan-500 hover:bg-gray-50 transition-colors duration-200"
              >
                {getNavText('medixbuy')}
              </Link>
              <Link
                href={`/${currentLocale}/contact`}
                onClick={() => setIsMobileMenuOpen(false)}
                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                  pathname.includes('/contact')
                    ? 'text-cyan-500 bg-cyan-50'
                    : 'text-gray-700 hover:text-cyan-500 hover:bg-gray-50'
                }`}
              >
                {getNavText('contact')}
              </Link>

              {/* Mobile Language Switcher */}
              <div className="px-3 py-2">
                <select
                  value={currentLocale}
                  onChange={(e) => {
                    const newLocale = e.target.value;
                    setIsMobileMenuOpen(false);
                    window.location.href = switchLanguage(newLocale);
                  }}
                  className="w-full appearance-none bg-white border border-gray-300 rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:border-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent cursor-pointer"
                >
                  <option value="en">🇺🇸 ENGLISH</option>
                  <option value="zh">🇨🇳 中文</option>
                  <option value="es">🇪🇸 ESPAÑOL</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}
