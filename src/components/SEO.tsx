'use client';

import { useEffect } from 'react';

interface SEOProps {
  title: string;
  description: string;
  keywords?: string;
  locale: string;
  path: string;
  image?: string;
}

export default function SEO({ 
  title, 
  description, 
  keywords, 
  locale, 
  path,
  image = '/images/logos/logo.png'
}: SEOProps) {
  const baseUrl = 'https://riomavix.com'; // 替换为实际域名
  const fullUrl = `${baseUrl}${path}`;
  
  // 生成hreflang链接
  const alternateLinks = [
    { locale: 'en', url: `${baseUrl}/en${path.replace(`/${locale}`, '')}` },
    { locale: 'zh', url: `${baseUrl}/zh${path.replace(`/${locale}`, '')}` },
    { locale: 'es', url: `${baseUrl}/es${path.replace(`/${locale}`, '')}` },
  ];

  useEffect(() => {
    // 设置页面标题
    document.title = title;

    // 清除之前的SEO meta标签
    const existingMetas = document.querySelectorAll('meta[data-seo="true"]');
    existingMetas.forEach(meta => meta.remove());

    const existingLinks = document.querySelectorAll('link[data-seo="true"]');
    existingLinks.forEach(link => link.remove());

    const existingScripts = document.querySelectorAll('script[data-seo="true"]');
    existingScripts.forEach(script => script.remove());

    // 创建meta标签的辅助函数
    const createMeta = (name: string, content: string, property?: boolean) => {
      const meta = document.createElement('meta');
      if (property) {
        meta.setAttribute('property', name);
      } else {
        meta.setAttribute('name', name);
      }
      meta.setAttribute('content', content);
      meta.setAttribute('data-seo', 'true');
      return meta;
    };

    // 创建link标签的辅助函数
    const createLink = (rel: string, href: string, hreflang?: string) => {
      const link = document.createElement('link');
      link.setAttribute('rel', rel);
      link.setAttribute('href', href);
      if (hreflang) {
        link.setAttribute('hreflang', hreflang);
      }
      link.setAttribute('data-seo', 'true');
      return link;
    };

    // 添加基础SEO meta标签
    document.head.appendChild(createMeta('description', description));
    if (keywords) {
      document.head.appendChild(createMeta('keywords', keywords));
    }
    document.head.appendChild(createMeta('robots', 'index, follow'));
    document.head.appendChild(createMeta('author', 'Riomavix'));
    document.head.appendChild(createMeta('company', 'Riomavix'));

    // 添加语言meta标签
    const langMeta = document.createElement('meta');
    langMeta.setAttribute('http-equiv', 'content-language');
    langMeta.setAttribute('content', locale);
    langMeta.setAttribute('data-seo', 'true');
    document.head.appendChild(langMeta);

    // 添加canonical链接
    document.head.appendChild(createLink('canonical', fullUrl));

    // 添加hreflang链接
    alternateLinks.forEach((link) => {
      document.head.appendChild(createLink('alternate', link.url, link.locale));
    });
    document.head.appendChild(createLink('alternate', `${baseUrl}/en${path.replace(`/${locale}`, '')}`, 'x-default'));

    // 添加Open Graph meta标签
    document.head.appendChild(createMeta('og:type', 'website', true));
    document.head.appendChild(createMeta('og:title', title, true));
    document.head.appendChild(createMeta('og:description', description, true));
    document.head.appendChild(createMeta('og:url', fullUrl, true));
    document.head.appendChild(createMeta('og:image', `${baseUrl}${image}`, true));
    document.head.appendChild(createMeta('og:locale', locale === 'zh' ? 'zh_CN' : locale === 'es' ? 'es_ES' : 'en_US', true));
    document.head.appendChild(createMeta('og:site_name', 'Riomavix', true));

    // 添加Twitter Card meta标签
    document.head.appendChild(createMeta('twitter:card', 'summary_large_image'));
    document.head.appendChild(createMeta('twitter:title', title));
    document.head.appendChild(createMeta('twitter:description', description));
    document.head.appendChild(createMeta('twitter:image', `${baseUrl}${image}`));

    // 添加结构化数据
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.setAttribute('data-seo', 'true');
    script.textContent = JSON.stringify({
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Riomavix",
      "url": baseUrl,
      "logo": `${baseUrl}/images/logos/logo.png`,
      "description": description,
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Calle de Goya, 21, 1D",
        "addressLocality": "Madrid",
        "addressCountry": "ES"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "availableLanguage": ["English", "Chinese", "Spanish"]
      },
      "sameAs": [
        "https://www.linkedin.com/company/riomavix-sl/"
      ]
    });
    document.head.appendChild(script);

    // 清理函数
    return () => {
      const metas = document.querySelectorAll('meta[data-seo="true"]');
      metas.forEach(meta => meta.remove());

      const links = document.querySelectorAll('link[data-seo="true"]');
      links.forEach(link => link.remove());

      const scripts = document.querySelectorAll('script[data-seo="true"]');
      scripts.forEach(script => script.remove());
    };
  }, [title, description, keywords, locale, fullUrl, image, baseUrl, path, alternateLinks]);

  return null;
}
